<?php

namespace Modules\Property\app\Filament\Resources;

use App\Forms\ChangePasswordForm;
use <PERSON>zhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use App\Shared\Helpers\FormComponents;
use Filament\Forms\Components\Group;
use Filament\Tables;
use App\Enums\RoleEnum;
use Filament\Forms\Get;
use Filament\Forms\Form;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Table;
use App\Helpers\FormHelper;
use Illuminate\Support\Str;
use App\Models\DocumentType;
use App\Enums\DocumentTypeEnum;
use App\Enums\PropertyTypeEnum;
use Filament\Resources\Resource;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Wizard;
use Illuminate\Support\Facades\Blade;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Repeater;
use Illuminate\Database\Eloquent\Model;
use Modules\Account\app\Models\Account;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Filament\Forms\Components\DatePicker;
use Illuminate\Database\Eloquent\Builder;
use Modules\Property\app\Filament\Resources\PropertyResource\RelationManagers\SyncPropertyStepsRelationManager;
use Modules\Property\app\Models\Property;
use Modules\Property\app\Models\Amenities;
use Modules\Property\app\Models\Attribute;
use Modules\Property\app\Models\Usability;
use Modules\Property\Enums\OwnershipDocumentTypeEnum;
use Modules\Property\Enums\PropertyStatus;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\MarkdownEditor;
use Modules\Property\app\Models\PropertyType;
use Cheesegrits\FilamentGoogleMaps\Fields\Map;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Cheesegrits\FilamentGoogleMaps\Fields\Geocomplete;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Modules\Property\app\Filament\Resources\PropertyResource\RelationManagers\UnitsRelationManager;
use Modules\Property\app\Filament\Resources\PropertyResource\Components\OwnershipDocument\DocumentFields;
use Modules\Property\app\Filament\Resources\PropertyResource\Components\OwnershipDocument\DocumentTypeSelect;
use Modules\Property\app\Filament\Resources\PropertyResource\Components\OwnershipDocument\DocumentUpload;
use Filament\Infolists\Infolist;
use App\Shared\Components\CustomInfoTab as Tab;
use App\Shared\Components\CustomInfoTabs;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Grid as InfolistGrid;
use Filament\Infolists\Components\Section as InfolistSection;
use Filament\Infolists\Components\View as InfolistView;
use app\Shared\Components\CustomSwiperImageEntry;
use Filament\Resources\Pages\ViewRecord;
use Modules\Property\Enums\EjarSyncStatus;
use Modules\Property\app\Filament\Resources\PropertyResource\Widgets\PropertyListOverview;
use Modules\Property\app\Filament\Resources\PropertyResource\RelationManagers\BrokersLogRelationManager;
use Modules\Organization\app\Models\Organization;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\Contracts\CanDisableOptions;
use App\Forms\Components\HijriDatePicker;
use Carbon\Carbon;
use Modules\Property\Enums\UnitPeriodType;

class PropertyResource extends Resource implements HasShieldPermissions
{
    protected static ?string $model = Property::class;

    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected static ?string $navigationGroup = 'Properties Management';

    protected const RADIO_DECK_COLOR = '#0f2c24';



    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Wizard::make([
                    Wizard\Step::make(__('Ownership Document'))
                        ->description(__('Manage property ownership document'))
                        ->icon('heroicon-o-document-text')
                        ->completedIcon('heroicon-o-check-badge')
                        ->schema([
                            Section::make(__('Document'))
                                ->relationship('documentOwnership')
                                ->schema([
                                    Grid::make(2)
                                        ->schema([
                                            Hidden::make('document_type_id')
                                                ->default(function () {
                                                    return DocumentType::where('key', DocumentTypeEnum::Ownership->value)->first()->id;
                                                }),
                                            Hidden::make('company_id')
                                                ->default(function($livewire) {
                                                    if(!auth()->user()->hasRole(RoleEnum::ADMIN)){
                                                        return auth()->user()->company_id;
                                                    }
                                                    return $livewire->data['company_id'] ?? null;
                                                }),
                                            DocumentTypeSelect::make(),
                                            ...DocumentFields::make(),
                                            DocumentUpload::make(),
                                        ])
                                ])
                        ]),
                    Wizard\Step::make(__('Owners'))
                        ->description(__('Manage property owners and brokers'))
                        ->icon('heroicon-o-users')
                        ->completedIcon('heroicon-o-check-badge')
                        ->schema([
                            FormHelper::brokerField(),
                            Repeater::make('owners')
                                ->deletable(fn(Get $get) => count($get('owners') ?? []) > 1)
                                ->relationship('onlyOwner')
                                ->label(__('Owners'))
                                ->schema([
                                    Select::make('ownerable_type')
                                        ->label(__('Owner Type'))
                                        ->options([
                                            Account::class => __('Individual'),
                                            Organization::class => __('Organization'),
                                        ])
                                        ->default(Account::class)
                                        ->required()
                                        ->live()
                                        ->native(false)
                                        ->afterStateUpdated(function ($state, callable $set, $get) {
                                            $set('ownerable_id', null);
                                            if ($state === Organization::class) {
                                                $set('is_representer', false);
                                            }
                                        }),
                                    Select::make('ownerable_id')
                                        ->label(function (Get $get) {
                                            return $get('ownerable_type') === Organization::class
                                                ? __('Organization')
                                                : __('Owner');
                                        })
                                        ->placeholder(function (Get $get) {
                                            return $get('ownerable_type') === Organization::class
                                                ? __('Select Organization')
                                                : __('Select Owner');
                                        })
                                        ->searchable()
                                        ->getSearchResultsUsing(function (string $search, Get $get, $livewire) {
                                            $representer = $livewire->data['onlyRepresentative']['ownerable_id'] ?? null;
                                            $ownerableType = $get('ownerable_type');
                                            if ($ownerableType === Organization::class) {
                                                return Organization::where(function ($query) use ($search) {
                                                    $query->where('name', '=', "{$search}")
                                                        ->orWhere('unified_number', '=', "{$search}");
                                                })
                                                ->get()
                                                ->mapWithKeys(function ($organization) {
                                                    return [$organization->id => "{$organization->name} - {$organization->unified_number}"];
                                                });
                                            } else {
                                                // Search in accounts
                                                return Account::where(function ($query) use ($search) {
                                                    $query->where('name', '=', "{$search}")
                                                        ->orWhere('national_id', '=', "{$search}");
                                                })
                                                ->when($representer, function ($query) use ($representer) {
                                                    return $query->where('id', '!=', $representer);
                                                })
                                                ->where('is_active', true)
                                                ->get()
                                                ->mapWithKeys(function ($account) {
                                                    return [$account->id => "{$account->name} - {$account->national_id}"];
                                                });
                                            }
                                        })
                                        ->getOptionLabelUsing(function ($value, Get $get) {
                                            $ownerableType = $get('ownerable_type');

                                            if ($ownerableType === Organization::class) {
                                                $organization = Organization::find($value);
                                                if ($organization) {
                                                    return "{$organization->name} - {$organization->unified_number}";
                                                }
                                            } else {
                                                $account = Account::find($value);
                                                if ($account) {
                                                    return "{$account->name} - {$account->national_id}";
                                                }
                                            }
                                            return null;
                                        })
                                        ->createOptionForm(function ($form, Get $get) {
                                            if ($get('ownerable_type') === Organization::class) {
                                                return [
                                                    Grid::make(2)
                                                        ->schema([
                                                            TextInput::make('name')
                                                                ->label(__('Organization Name'))
                                                                ->placeholder(__('Enter organization name'))
                                                                ->required()
                                                                ->validationMessages([
                                                                    'required' => __('Organization Name is required'),
                                                                ]),
                                                            TextInput::make('ownership_document_number')
                                                                ->label(__('Ownership Document Number'))
                                                                ->placeholder(__('Enter ownership document number'))
                                                                ->numeric()
                                                                ->validationMessages([
                                                                    'required' => __('Ownership Document Number is required'),
                                                                ]),
                                                            TextInput::make('unified_number')
                                                                ->label(__('Unified Number'))
                                                                ->numeric()
                                                                ->required()
                                                                ->unique('organizations', 'unified_number')
                                                                ->placeholder('Just Start with 70xxxxxxxx')
                                                                ->extraInputAttributes([
                                                                    'maxlength' => '10',
                                                                    'minlength' => '10',
                                                                    'pattern' => '70[0-9]{8}',
                                                                    'oninput' => "
                                                                        this.value = this.value.replace(/[^0-9]/g, '').substring(0, 10);
                                                                        if (this.value.length > 1 && !this.value.startsWith('70')) {
                                                                            this.value = '70';
                                                                        }
                                                                        this.dispatchEvent(new Event('input'));
                                                                    "
                                                                ])
                                                                ->rules([
                                                                    'required',
                                                                    'numeric',
                                                                    'digits:10',
                                                                    'regex:/^70\d{8}$/',
                                                                ])
                                                                ->validationMessages([
                                                                    'required' => __('Unified Number is required'),
                                                                    'digits' => __('Unified Number must be exactly 10 digits'),
                                                                    'numeric' => __('Unified Number must contain only numbers'),
                                                                    'regex' => __('Unified Number must start with 70'),
                                                                    'unique' => __('Unified Number is already taken'),
                                                                ]),
                                                            TextInput::make('registration_number')
                                                                ->label(__('Registration Number'))
                                                                ->placeholder(__('Enter Registration Number'))
                                                                ->required()
                                                                ->validationMessages([
                                                                    'required' => __('Registration Number is required'),
                                                                ]),
                                                            DatePicker::make('registration_date')
                                                                ->label(__('Registration Date'))
                                                                ->placeholder(__('Enter Registration Date'))
                                                                ->required()
                                                                ->before(now())
                                                                ->native(false)
                                                                ->validationMessages([
                                                                    'required' => __('Registration Date is required'),
                                                                ]),
                                                        ])
                                                ];
                                            } else {
                                                $updateFullName = function ($set, $get) {
                                                    $set('name', trim(
                                                        $get('first_name') . ' ' .
                                                        $get('second_name') . ' ' .
                                                        $get('third_name') . ' ' .
                                                        $get('last_name')
                                                    ));
                                                };

                                                return [
                                                    Hidden::make('name'),
                                                    Hidden::make('lang')->default('ar'),
                                                    Hidden::make('is_active')->default(true),

                                                    Grid::make(2)
                                                        ->schema([
                                                            TextInput::make('first_name')
                                                                ->required()
                                                                ->label(__('First Name'))
                                                                ->placeholder(__('Enter first name'))

                                                                ->afterStateUpdated($updateFullName)
                                                                ->validationMessages([
                                                                    'required' => __('First Name is required'),
                                                                ]),
                                                            TextInput::make('second_name')
                                                                ->required()
                                                                ->label(__('Second Name'))
                                                                ->placeholder(__('Enter second name'))

                                                                ->afterStateUpdated($updateFullName)
                                                                ->validationMessages([
                                                                    'required' => __('Second Name is required'),
                                                                ]),
                                                        ]),

                                                    Grid::make(2)
                                                        ->schema([
                                                            TextInput::make('third_name')
                                                                ->required()
                                                                ->label(__('Third Name'))
                                                                ->placeholder(__('Enter third name'))

                                                                ->afterStateUpdated($updateFullName)
                                                                ->validationMessages([
                                                                    'required' => __('Third Name is required'),
                                                                ]),
                                                            TextInput::make('last_name')
                                                                ->required()
                                                                ->label(__('Last Name'))
                                                                ->placeholder(__('Enter last name'))

                                                                ->afterStateUpdated($updateFullName)
                                                                ->validationMessages([
                                                                    'required' => __('Last Name is required'),
                                                                ]),
                                                        ]),

                                                    Grid::make(2)
                                                        ->schema([
                                                            TextInput::make('email')
                                                                ->email()
                                                                ->required()
                                                                ->unique('accounts', 'email')
                                                                ->label(__('Email')),
                                                            TextInput::make('phone')
                                                                ->label(__('Phone Number'))
                                                                ->tel()
                                                                ->required()
                                                                ->prefix('+966')
                                                                ->placeholder('5xxxxxxxx')
                                                                ->rules([
                                                                    'required',
                                                                    'regex:/^5[0-9]{8}$/',
                                                                    'size:9',
                                                                    fn () => function($attribute, $value, $fail) {
                                                                        $fullPhone = '+966' . $value;
                                                                        if (Account::where('phone', $fullPhone)->exists()) {
                                                                            $fail(__('This phone number is already registered.'));
                                                                        }
                                                                    }
                                                                ])
                                                                ->validationMessages([
                                                                    'regex' => __('Phone number must start with 5 and be 9 digits.'),
                                                                    'size' => __('Phone number must be exactly 9 digits.')
                                                                ])
                                                                ->extraInputAttributes([
                                                                    'maxlength' => '9',
                                                                    'pattern' => '5[0-9]*',
                                                                    'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "").substring(0, 9)'
                                                                ])
                                                                ->dehydrateStateUsing(fn ($state) => '+966' . $state),
                                                        ]),

                                                    Grid::make(2)
                                                        ->schema([
                                                            FormComponents::registerIdFieldSet(),
                                                            FormComponents::birthDateInput('birth_date')
                                                                ->label(__('Birth Date'))
                                                                ->helperText(__('Must be at least 18 years old')),
                                                        ]),

                                                    Grid::make(2)
                                                        ->schema([
                                                            ...ChangePasswordForm::make($form->getOperation(), true),
                                                        ]),

                                                    Hidden::make('accountRoles')
                                                        ->default([
                                                            [
                                                                'role' => 'owner',
                                                                'is_default' => true
                                                            ]
                                                        ]),
                                                ];
                                            }
                                        })
                                        ->createOptionUsing(function (array $data, Get $get) {
                                            $ownerableType = $get('ownerable_type');

                                            if ($ownerableType === Organization::class) {

                                                $organization = Organization::create([
                                                    'name' => $data['name'],
                                                    'unified_number' => $data['unified_number'],
                                                    'ownership_document_number' => $data['ownership_document_number'],
                                                ]);
                                                return $organization->id;
                                            } else {
                                                $account = Account::create([
                                                    'name' => $data['name'],
                                                    'first_name' => $data['first_name'],
                                                    'second_name' => $data['second_name'],
                                                    'third_name' => $data['third_name'],
                                                    'last_name' => $data['last_name'],
                                                    'email' => $data['email'],
                                                    'phone' => $data['phone'],
                                                    'national_id' => $data['national_id'],
                                                    'id_type' => $data['id_type'],
                                                'country_of_issue' => $data['country_of_issue'] ?? 'SA',
                                                'birth_date' => $data['birth_date'],
                                                    'lang' => $data['lang'],
                                                    'is_active' => $data['is_active'],
                                                    'password' => $data['password'],
                                                ]);

                                                if (isset($data['accountRoles']) && is_array($data['accountRoles'])) {
                                                    foreach ($data['accountRoles'] as $roleData) {
                                                        $account->accountRoles()->create([
                                                            'role' => $roleData['role'],
                                                            'is_default' => $roleData['is_default'] ?? true,
                                                        ]);
                                                    }
                                                }

                                                return $account->id;
                                            }
                                        })
                                        ->createOptionAction(fn(Action $action) => $action->visible(function (Get $get) {
                                                $ownerableType = $get('ownerable_type');
                                                if ($ownerableType === Organization::class) {
                                                    return auth()->user()->can('create', Organization::class);
                                                } else {
                                                    return auth()->user()->can('create', Account::class);
                                                }
                                            })
                                        )
                                        ->debounce(500)
                                        ->preload()
                                        ->required()
                                        ->live()
                                        ->disableOptionWhen(function (Component & CanDisableOptions $component, string $value, mixed $state) {
                                            $repeater = $component->getParentRepeater();

                                            if (!$repeater) {
                                                return false;
                                            }

                                            $statePath = $component->getContainer()->getStatePath(false);

                                            $currentOwnerableType = $repeater->getState()[$statePath]['ownerable_type'] ?? null;



                                            return collect($repeater->getState())
                                                ->filter(function ($item) use ($currentOwnerableType, $value, $statePath, $repeater) {
                                                    if ($item === $repeater->getState()[$statePath]) {
                                                        return false;
                                                    }
                                                    return isset($item['ownerable_type']) &&
                                                           $item['ownerable_type'] === $currentOwnerableType &&
                                                           isset($item['ownerable_id']) &&
                                                           $item['ownerable_id'] == $value;
                                                })
                                                ->isNotEmpty();
                                        })
                                        ->validationMessages([
                                            'required' => __('Select Owner is required'),
                                        ]),
                                    TextInput::make('percentage')
                                        ->numeric()
                                        ->required()
                                        ->label(__('Percentage'))
                                        ->placeholder(__('Percentage'))
                                        ->minValue(1)
                                        ->maxValue(100)
                                        ->default(0)
                                        ->validationMessages([
                                            'required' => __('Enter percentage is required'),
                                            'min' => __('min value percentage is 1%'),
                                            'max' => __('max value percentage is 100%'),
                                        ]),
                                    TextInput::make('ownership_document_number')
                                    ->label(__('Ownership Document Number'))
                                    ->placeholder(__('Enter Ownership Document Number'))
                                    ->nullable(),
                                    Toggle::make('is_representer')
                                        ->inline(false)
                                        ->label(__('Mark As Representer'))
                                        ->fixIndistinctState()
                                        ->onIcon('heroicon-m-bolt')
                                        ->offIcon('heroicon-m-user')
                                        ->live()
                                        ->afterStateUpdated(function ($get , $set,$livewire,$state) {
                                            $representer = $get('../../add_representer');
                                            if($representer){
                                                $owners = collect($get('../../owners') ?? []);
                                                if($owners->contains('is_representer', true)){
                                                    $set('../../add_representer', false);
                                                }
                                            }
                                        })
                                        ->hidden(function (Get $get, $livewire) {
                                            $owners = collect($get('../../owners') ?? []);
                                            $representer = $get('../../add_representer');
                                            $ownerableType = $get('ownerable_type');
                                            if(!$representer && count($owners) > 1 && $ownerableType !== Organization::class){
                                                return false;
                                            }
                                            return true;
                                        }),
                                ])
                                ->deleteAction(
                                    fn (Action $action) => $action
                                        ->before(function (array $state, Get $get,$livewire) {
                                            $owners = $livewire->data['owners'] ?? [];

                                            // If we're deleting and only 2 owners exist
                                            if (count($owners) === 2) {
                                                foreach ($owners as &$owner) {
                                                    if ($owner['is_representer']) {
                                                        $owner['is_representer'] = false;
                                                        break;
                                                    }
                                                }
                                                unset($owner); // Unset the reference
                                                $livewire->data['owners'] = $owners;
                                            }
                                        })
                                )
                                ->live()
                                ->rule(function (callable $get) {
                                    return function ($attribute, $value, $fail) use ($get) {
                                        $owners = collect($get('owners') ?? []);
                                        $totalPercentage = $owners->map(function ($owner) {
                                            return (int)($owner['percentage'] ?? 0);
                                        })->sum();
                                        if ($totalPercentage !== 100) {
                                            $fail(__('The total percentage of all owners must equal 100%.'));
                                        }

                                        $allOrganizations = $owners->isNotEmpty() && $owners->every(function ($owner) {
                                            return isset($owner['ownerable_type']) && $owner['ownerable_type'] === Organization::class;
                                        });

                                        $hasRepresenter = $owners->contains('is_representer', true) || $get('add_representer');

                                        if ($allOrganizations && !$hasRepresenter) {
                                            $fail(__('When all owners are organizations, you must add a representer.'));
                                        }
                                    };
                                })
                                ->minItems(1)
                                ->columns(2)
                                ->visible(function (Get $get,$livewire) {
                                    if($livewire instanceof ViewRecord) {
                                        return false;
                                    }
                                    return true;
                                }),
                            Toggle::make('add_representer')
                                ->label(__('add representer'))
                                ->onIcon('heroicon-m-bolt')
                                ->offIcon('heroicon-m-user')
                                ->live()
                                ->afterStateUpdated(function ($get , $set,$livewire,$state) {
                                    $set('onlyRepresentative.ownerable_id',null);
                                })
                                ->afterStateHydrated(function (callable $set,?Model $record) {
                                    $representer = $record?->onlyRepresentative;
                                    if ($representer) {
                                        $set('add_representer', true);
                                    }
                                })
                                ->hidden(function (Get $get,$livewire) {
                                    if($livewire instanceof ViewRecord) {
                                        return false;
                                    }
                                    $owners = collect($get('owners') ?? []);

                                    return $owners->contains('is_representer', true);
                                }),

                            Section::make(__('Representer'))
                                ->label(__('Representer'))
                                ->relationship('onlyRepresentative')
                                ->schema([
                                    Grid::make(1)
                                        ->schema([
                                            Select::make('ownerable_id')
                                                ->label(__('Representer'))
                                                ->placeholder(__('Select representer'))
                                                ->searchable(['national_id', 'name'])
                                                ->native(false)
                                                ->required()
                                                ->getSearchResultsUsing(function (string $search, Get $get,$livewire) {
                                                    $accountOwnerIds = collect($livewire->data['owners'])
                                                        ->filter(function ($owner) {
                                                            return isset($owner['ownerable_type']) &&
                                                                   $owner['ownerable_type'] === Account::class;
                                                        })
                                                        ->pluck('ownerable_id')
                                                        ->filter()
                                                        ->toArray();
                                                    return Account::where(function ($query) use ($search) {
                                                        $query->where('name', '=', "{$search}")
                                                            ->orWhere('national_id', '=', "{$search}");
                                                    })
                                                    ->where('is_active', true)
                                                    ->whereNotIn('id', $accountOwnerIds)  // Exclude owner IDs
                                                    ->get()
                                                    ->mapWithKeys(function ($account) {
                                                        return [$account->id => "{$account->name} - {$account->national_id}"];
                                                    });
                                                })
                                                ->getOptionLabelUsing(function ($value) {
                                                    // Only query the database for the selected value
                                                    $account = Account::find($value);
                                                    if ($account) {
                                                        return "{$account->name} - {$account->national_id}";
                                                    }
                                                    return null;
                                                })
                                                ->debounce(500)
                                                ->createOptionForm(function ($form) {
                                                    $updateFullName = function ($set, $get) {
                                                        $set('name', trim(
                                                            $get('first_name') . ' ' .
                                                            $get('second_name') . ' ' .
                                                            $get('third_name') . ' ' .
                                                            $get('last_name')
                                                        ));
                                                    };

                                                    return [
                                                        Hidden::make('name'),
                                                        Hidden::make('lang')->default('ar'),
                                                        Hidden::make('is_active')->default(true),

                                                        Grid::make(2)
                                                            ->schema([
                                                                TextInput::make('first_name')
                                                                    ->required()
                                                                    ->label(__('First Name'))
                                                                    ->placeholder(__('Enter first name'))

                                                                    ->afterStateUpdated($updateFullName),
                                                                TextInput::make('second_name')
                                                                    ->required()
                                                                    ->label(__('Second Name'))
                                                                    ->placeholder(__('Enter second name'))

                                                                    ->afterStateUpdated($updateFullName),
                                                            ]),

                                                        Grid::make(2)
                                                            ->schema([
                                                                TextInput::make('third_name')
                                                                    ->required()
                                                                    ->label(__('Third Name'))
                                                                    ->placeholder(__('Enter third name'))

                                                                    ->afterStateUpdated($updateFullName),
                                                                TextInput::make('last_name')
                                                                    ->required()
                                                                    ->label(__('Last Name'))
                                                                    ->placeholder(__('Enter last name'))

                                                                    ->afterStateUpdated($updateFullName),
                                                            ]),

                                                        Grid::make(2)
                                                            ->schema([
                                                                TextInput::make('email')
                                                                    ->email()
                                                                    ->required()
                                                                    ->unique('accounts', 'email')
                                                                    ->label(__('Email')),
                                                                TextInput::make('phone')
                                                                    ->label(__('Phone Number'))
                                                                    ->tel()
                                                                    ->required()
                                                                    ->prefix('+966')
                                                                    ->placeholder('5xxxxxxxx')
                                                                    ->rules([
                                                                        'required',
                                                                        'string',
                                                                        'regex:/^5[0-9]{8}$/',
                                                                        'size:9',
                                                                        fn() => function ($attribute, $value, $fail) {
                                                                            $fullPhone = '+966' . $value;
                                                                            if (Account::where('phone', $fullPhone)->exists()) {
                                                                                $fail(__('Phone number already exists'));
                                                                            }
                                                                        }
                                                                    ])
                                                                    ->validationMessages([
                                                                        'regex' => __('Phone number must start with 5 and be 9 digits.'),
                                                                        'size' => __('Phone number must be exactly 9 digits.')
                                                                    ])
                                                                    ->extraInputAttributes([
                                                                        'maxlength' => '9',
                                                                        'pattern' => '5[0-9]*',
                                                                        'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "").substring(0, 9)'
                                                                    ])
                                                                    ->dehydrateStateUsing(fn ($state) => '+966' . $state),
                                                            ]),

                                                        Grid::make(2)
                                                            ->schema([
                                                                FormComponents::idFieldSet(),
                                                                FormComponents::birthDateInput('birth_date')
                                                                    ->label(__('Birth Date'))
                                                                    ->helperText(__('Must be at least 18 years old')),
                                                            ]),

                                                        Grid::make(2)
                                                            ->schema([
                                                                ...ChangePasswordForm::make($form->getOperation(), true),
                                                            ]),

                                                        Hidden::make('accountRoles')
                                                            ->default([
                                                                [
                                                                    'role' => 'owner',
                                                                    'is_default' => true
                                                                ]
                                                            ]),
                                                    ];
                                                })
                                                ->createOptionUsing(function (array $data) {
                                                    $account = Account::create([
                                                        'name' => $data['name'],
                                                        'first_name' => $data['first_name'],
                                                        'second_name' => $data['second_name'],
                                                        'third_name' => $data['third_name'],
                                                        'last_name' => $data['last_name'],
                                                        'email' => $data['email'],
                                                        'phone' => $data['phone'],
                                                        'national_id' => $data['national_id'],
                                                        'id_type' => $data['id_type'],
                                                        'country_of_issue' => $data['country_of_issue'] ?? 'SA',
                                                        'birth_date' => $data['birth_date'],
                                                        'lang' => $data['lang'],
                                                        'is_active' => $data['is_active'],
                                                        'password' => $data['password'],
                                                    ]);

                                                    if (isset($data['accountRoles']) && is_array($data['accountRoles'])) {
                                                        foreach ($data['accountRoles'] as $roleData) {
                                                            $account->accountRoles()->create([
                                                                'role' => $roleData['role'],
                                                                'is_default' => $roleData['is_default'] ?? true,
                                                            ]);
                                                        }
                                                    }

                                                    return $account->id;
                                                })
                                                ->required()
                                            ->validationMessages([
                                                'required' => __('Select Representer is required'),
                                            ]),
                                            Hidden::make('percentage')
                                                ->default(0),
                                            Hidden::make('ownerable_type')
                                                ->default(Account::class),
                                            Hidden::make('is_representer')
                                                ->default(true),
                                        ]),
                                ])
                                ->hidden(function (Get $get) {
                                    $owners = collect($get('owners') ?? []);
                                    return $owners->contains('is_representer', true);
                                })
                                ->visible(fn(Get $get)=>$get('add_representer')),
                            Section::make(__('Document'))
                                ->relationship('document')
                                ->schema([
                                    Grid::make(2)
                                        ->schema([
                                            Hidden::make('document_type_id')
                                                ->default(function () {
                                                    return DocumentType::where('key', DocumentTypeEnum::Representative->value)->first()?->id;
                                                }),
                                            Hidden::make('company_id')
                                                ->default(function($livewire) {
                                                    if(!auth()->user()->hasRole(RoleEnum::ADMIN)){
                                                        return auth()->user()->company_id;
                                                    }
                                                    return $livewire->data['company_id'] ?? null;
                                                }),
                                            TextInput::make('metadata.document_name_and_type')
                                                ->label(__('Document Name and Type'))
                                                ->placeholder(__('Document Name and Type'))
                                                ->required()
                                                ->validationMessages([
                                                    'required' => __('Document Name and Type is required'),
                                                ]),
                                            TextInput::make('metadata.document_number')
                                                ->label(__('Document Number'))
                                                ->placeholder(__('Document Number'))
                                                ->required()
                                                ->validationMessages([
                                                    'required' => __('Document Number is required'),
                                                ]),
                                            HijriDatePicker::make('metadata.release_date')
                                                ->label(__('Release Date'))
                                                ->placeholder(__('Release Date'))
                                                ->syncWith('document_date')
                                                ->showConvertedDate()
                                                ->live()
                                                ->required()
                                                ->validationMessages([
                                                    'required' => __('Release Date is required'),
                                                ]),

                                            HijriDatePicker::make('metadata.expiration_date')
                                                ->label(__('Expiration Date'))
                                                ->placeholder(__('Expiration Date'))
                                                ->syncWith('document_date')
                                                ->showConvertedDate()
                                                ->hideSwitcher()
                                                ->minDate(fn (Get $get): string =>
                                                $get('metadata.release_date')
                                                    ? \Carbon\Carbon::parse($get('metadata.release_date'))->addDay()->toDateString()
                                                    : now()->toDateString()
                                                )
                                                ->validationMessages([
                                                    'required' => __('Expiration Date is required'),
                                                ]),

                                            TextInput::make('metadata.released_by')
                                                ->label(__('Released By'))
                                                ->placeholder(__('Released By'))
                                                ->required()
                                                ->validationMessages([
                                                    'required' => __('Released By is required'),
                                                ]),
                                            TextInput::make('metadata.released_in')
                                                ->label(__('Released In'))
                                                ->placeholder(__('Released In'))
                                                ->required()
                                                ->validationMessages([
                                                    'required' => __('Released In is required'),
                                                ]),
                                            SpatieMediaLibraryFileUpload::make('upload_contract')
                                                ->label(__('Upload Contract Authorization'))
                                                ->downloadable()
                                                ->openable()
                                                ->panelLayout('grid')
                                                ->reorderable()
                                                ->multiple()
                                                ->maxSize(5120)
                                                ->collection('upload_contract')
                                                ->imageEditorAspectRatios([
                                                    '16:9',
                                                    '4:3',
                                                    '1:1',
                                                ])
                                                ->columnSpanFull()
                                                ->required()
                                           ->validationMessages([
                                                'required' => __('Upload Contract Authorization is required'),
                                            ]),
                                        ])
                                ])
                                ->hidden(function (Get $get) {
                                    $owners = collect($get('owners') ?? []);
                                    $representer = $get('add_representer');
                                    return !$owners->contains('is_representer', true) && !$representer;
                                }),
                        ]),
                    Wizard\Step::make(__('Property Details'))
                        ->description(__('Manage property details'))
                        ->icon('heroicon-o-building-office')
                        ->completedIcon('heroicon-o-check-badge')
                        ->schema([
                            Grid::make(2)
                                ->schema([
                                    Tabs::make('Tabs')
                                        ->tabs([
                                            Tabs\Tab::make(__('Arabic'))
                                                ->schema([
                                                    TextInput::make('name.ar')
                                                        ->label(__(' Name'))
                                                        ->placeholder(__(' Name'))
                                                        ->required()
                                                        ->validationMessages([
                                                            'required' => __('Name is required'),
                                                        ]),
                                                    MarkdownEditor::make('description.ar')
                                                        ->label(__(' description'))
                                                        ->placeholder(__(' description'))
                                                        ->required()
                                                        ->validationMessages([
                                                            'required' => __('Description is required'),
                                                        ]),
                                                ]),
                                            Tabs\Tab::make(__('English'))
                                                ->schema([
                                                    TextInput::make('name.en')
                                                        ->label(__(' name'))
                                                        ->placeholder(__(' name'))
                                                        ->required()
                                                        ->validationMessages([
                                                            'required' => __('name is required'),
                                                        ]),
                                                    MarkdownEditor::make('description.en')
                                                        ->label(__(' description'))
                                                        ->placeholder(__(' description'))
                                                        ->required()
                                                        ->validationMessages([
                                                            'required' => __('description is required'),
                                                        ]),
                                                ]),

                                        ])->columnSpan(1)
                                        ->extraAttributes(['style' => 'min-height: 100%;']),

                                    Grid::make(1)
                                        ->schema([
                                            Select::make('property_type_id')
                                                ->relationship(
                                                    name: 'property_type',
                                                    titleAttribute: 'name',
                                                    modifyQueryUsing: fn($query) => $query->where('property_type', PropertyTypeEnum::Property->value)
                                                )
                                                ->label(__('Property Type'))
                                                ->placeholder(__('Property Type'))
                                                ->searchable(['name'])
                                                ->preload()
                                                ->required()
                                                ->live()
                                                ->afterStateUpdated(function ($state, callable $set, $get) {
                                                    $set('attributes', []);

                                                    if ($state) {
                                                        $requiredAttributes = Attribute::whereHas('propertyTypes', function ($query) use ($state) {
                                                            $query->where('property_type_id', $state);
                                                        })->where('is_required', true)->get();

                                                        foreach ($requiredAttributes as $index => $attribute) {
                                                            $set("attributes.{$index}.attribute_id", $attribute->id);
                                                        }
                                                    }
                                                })
                                           ->validationMessages([
                                               'required' => __('Property Type is required'),
                                           ]),
                                            Select::make('usability_id')
                                                ->relationship(name: 'usability', titleAttribute: 'name')
                                                ->label(__('Usability'))
                                                ->placeholder(__('Usability'))
                                                ->searchable(['name'])
                                                ->preload()
                                                ->required()
                                                ->createOptionForm(fn(Form $form) => UsabilityResource::form($form))
                                                ->createOptionAction(
                                                    fn(Action $action) => $action->visible(fn() => auth()->user()->can('create', Usability::class))
                                                        ->modalWidth('2xl')
                                                )
                                           ->validationMessages([
                                                'required' => __('Usability is required'),
                                            ]),
                                            TextInput::make('number')
                                                ->label(__('Property Number'))
                                                ->placeholder(__('Property Number'))
                                                ->required()
                                           ->validationMessages([
                                                'required' => __('Property Number is required'),
                                            ]),
                                            HijriDatePicker::make('building_date')
                                                ->label(__('Building Date'))
                                                ->placeholder(__('Building Date'))
                                                ->showConvertedDate()
                                                ->syncWith('building_date')
                                                ->live()
                                                ->validationMessages([
                                                    'required' => __('Building Date is required'),
                                                ])
                                                ->afterStateUpdated(function ($state, callable $set, $get) {
                                                    $units = $get('units') ?? [];
                                                    foreach ($units as $key => $unit) {
                                                        $set("units.{$key}.building_date", $state);
                                                    }
                                                }),
                                            Select::make('amenities')
                                                ->relationship('amenities', 'name')
                                                ->label(__('Amenities'))
                                                ->placeholder(__('Amenities'))
                                                ->multiple()
                                                ->searchable(['name'])
                                                ->preload()
                                                ->validationMessages([
                                                    'required' => __('Amenities is required'),
                                                ])
                                                ->createOptionForm(fn(Form $form) => AmenitiesResource::form($form))
                                                ->createOptionAction(
                                                    fn(Action $action) => $action->visible(fn() => auth()->user()->can('create', Amenities::class))
                                                        ->modalWidth('2xl')
                                                )->createOptionUsing(function (array $data) {
                                                    $amenity = Amenities::create(
                                                        ['name' => $data['name'], 'icon' => $data['icon'], 'amenities_category_id' => $data['amenities_category_id']]
                                                    );
                                                    return $amenity->id;
                                                }),
                                        ])->columnSpan(1),
                                    Repeater::make('attributes')
                                        ->relationship('attributes')
                                        ->label(__('attributes'))
                                        ->collapsed(false)
                                        ->collapsible() // Enable collapsible behavior
                                        ->validationMessages([
                                            'required' => __('Attributes is required'),
                                        ])
                                        ->itemLabel(function (array $state): ?string {
                                            if (!isset($state['attribute_id'])) {
                                                return null;
                                            }

                                            $attribute = Attribute::find($state['attribute_id']);
                                            return $attribute ? $attribute->name : null;
                                        })
                                        ->schema([
                                            Select::make('attribute_id')
                                                ->options(function (Get $get, $livewire) {
                                                    // $propertyTypeId = $livewire->data['property_type_id'] ?? null;
                                                    $propertyTypeId = $get('../../property_type_id');
                                                    if (!$propertyTypeId) {
                                                        return [];
                                                    }
                                                    return Attribute::whereHas('propertyTypes', function ($query) use ($propertyTypeId) {
                                                        $query->where('property_types.id', $propertyTypeId);
                                                    })
                                                        ->pluck('name', 'id')
                                                        ->toArray();
                                                })
                                                ->disabled(function ($get) {
                                                    $attributeId = $get('attribute_id');
                                                    if (!$attributeId) {
                                                        return false;
                                                    }

                                                    $attribute = Attribute::find($attributeId);
                                                    return $attribute && $attribute->is_required === 1;
                                                })
                                                ->dehydrated()
                                                ->label(__('attributes'))
                                                ->placeholder(__('attributes'))
                                                ->searchable(['name'])
                                                ->preload()
                                                ->validationMessages([
                                                    'required' => __('Attributes is required'),
                                                ])
                                                ->disableOptionsWhenSelectedInSiblingRepeaterItems()
                                                ->required(),
                                            TextInput::make('value')
                                                ->label(__('Value'))
                                                ->validationMessages([
                                                    'required' => __('Value is required'),
                                                ])
                                                ->placeholder(__('Value'))
                                                ->required(),
                                        ])
                                        ->deleteAction(
                                            fn(Action $action) => $action
                                                ->requiresConfirmation()
                                                ->modalHeading(__('Delete Attribute'))
                                                ->modalDescription(__('Are you sure you want to delete this attribute?'))
                                                ->before(function ($record, $state, $arguments) use ($action) {
                                                    if (isset($state[$arguments['item']]['attribute_id'])) {
                                                        $attributeId = $state[$arguments['item']]['attribute_id'];
                                                        $attribute = Attribute::find($attributeId);

                                                        if ($attribute && $attribute->is_required) {
                                                            Notification::make()
                                                                ->danger()
                                                                ->title(__('Delete Failed'))
                                                                ->body(__('This attribute is required and cannot be deleted.'))
                                                                ->send();
                                                            $action->cancel();
                                                        }
                                                    }
                                                })
                                        )
                                        ->defaultItems(0)
                                        ->columns(2),
                                    SpatieMediaLibraryFileUpload::make('attachment')
                                        ->label(__('Attachment'))
                                        ->image()
                                        ->imageEditor()
                                        ->maxSize(5120)
                                        ->collection('property_images')
                                        ->imageEditorAspectRatios([
                                            '16:9',
                                            '4:3',
                                            '1:1',
                                        ])
                                        ->downloadable()
                                        ->openable()
                                        ->panelLayout('grid')
                                        ->reorderable()
                                        ->multiple()
                                        ->validationMessages([
                                            'required' => __('Attachment is required'),
                                            'max' => __('Image size must not exceed 5MB.'),
                                        ]),
                                ]),
                            Grid::make(3)
                                ->label(__('Meters'))
                                ->relationship('meters')
                                ->schema([
                                    Group::make([
                                        TextInput::make('water_meter')
                                            ->label(__('Water Meter'))
                                            ->placeholder(__('Enter water meter number'))
                                            ->nullable()
                                            ->unique(
                                                table: 'property_meters',
                                                column: 'water_meter',
                                                ignorable: fn ($record) => $record
                                            )
                                            ->dehydrated()
                                            ->prefixIcon('heroicon-o-beaker')
                                            ->maxLength(50)
                                            ->extraAttributes([
                                                'class' => 'bg-gray-50/50',
                                            ])
                                            ->validationMessages([
                                                'unique' => __('This water meter number is already in use'),
                                            ])

                                    ])->columnSpan(1),

                                    Group::make([
                                        TextInput::make('gas_meter')
                                            ->label(__('Gas Meter'))
                                            ->placeholder(__('Enter gas meter number'))
                                            ->nullable()
                                            ->unique(
                                                table: 'property_meters',
                                                column: 'gas_meter',
                                                ignorable: fn ($record) => $record
                                            )
                                            ->dehydrated()
                                            ->prefixIcon('heroicon-o-fire')
                                            ->maxLength(50)
                                            ->extraAttributes([
                                                'class' => 'bg-gray-50/50',
                                            ])
                                            ->validationMessages([
                                                'unique' => __('This Gas meter number is already in use'),
                                            ])
                                    ])->columnSpan(1),

                                    Group::make([
                                        TextInput::make('electronic_meter')
                                            ->label(__('Electronic Meter'))
                                            ->placeholder(__('Enter electronic meter number'))
                                            ->nullable()
                                            ->unique(
                                                table: 'property_meters',
                                                column: 'electronic_meter',
                                                ignorable: fn ($record) => $record
                                            )
                                            ->dehydrated()
                                            ->prefixIcon('heroicon-o-bolt')
                                            ->maxLength(50)
                                            ->extraAttributes([
                                                'class' => 'bg-gray-50/50',
                                            ])
                                            ->validationMessages([
                                                'unique' => __('This electronic meter number is already in use'),
                                            ])
                                    ])->columnSpan(1),
                                ])
                                ->columns(3)
                                ->extraAttributes([
                                    'class' => 'mt-4 mb-4', // Add some vertical spacing
                                ]),
                        ]),
                    Wizard\Step::make(__('Property Address'))
                        ->description(__('Manage property attributes'))
                        ->icon('heroicon-o-map-pin')
                        ->completedIcon('heroicon-o-check-badge')
                        ->schema([
                            Geocomplete::make('address')
                                ->label(__('Search Location'))
                                ->placeholder(__('Search Location'))

                                ->types(['address'])
                                ->required()
                                ->validationMessages([
                                    'required' => __('Search Location is required'),
                                ]),

                            Map::make('map_location')
                                ->label(__('Location on Map'))
                                ->autocomplete('address')
                                ->autocompleteReverse('address')
                                ->reverseGeocode(['address'])
                                ->defaultZoom(13)
                                ->clickable()
                                ->draggable()
                                ->defaultLocation(function ($get) {
                                    if ($get('lat') && $get('lng')) {
                                        return [
                                            'lat' => $get('lat'),
                                            'lng' => $get('lng'),
                                        ];
                                    }
                                })
                                ->reverseGeocode([
                                    'street' => '%S',
                                    'postal_code' => '%z',
                                    'build_number' => '%n',
                                    'district' => '%A3 %D',
                                    'city' => '%A2',
                                    'region' => '%A1',
                                ])
                                ->autocompleteReverse(true)
                                ->geolocate()
                                ->geolocateLabel('Get Location')
                                ->geolocateOnLoad(true, false)
                                ->reactive()
                                ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                    if ($state) {
                                        $set('lat', $state['lat']);
                                        $set('lng', $state['lng']);

                                        // Find region by name when map is updated
                                        if ($get('region')) {
                                            $suggestedName = $get('region');
                                            $regionName = $get('region');
                                            if (Str::contains(Str::lower($regionName), ' province')) {
                                                $regionName = str_ireplace(' province', '', $regionName);
                                            }
                                            $searchKey = '%'.$regionName.'%';
                                            $region = \App\Models\Region::whereRaw("LOWER(name->'$.en') LIKE ?", [strtolower($searchKey)])
                                                ->orWhereRaw("LOWER(name->'$.ar') LIKE ?", [strtolower($searchKey)])
                                                ->first();

                                            if ($region) {
                                                $set('region_id', $region->id);
                                                $set('region', $suggestedName);
                                            }
                                        }
                                        if (!$get('city')) {
                                            $set('city_id', null);
                                            $set('district_id', null);
                                        }
                                    }
                                })
                                ->dehydrated(false),

                            Hidden::make('lat')
                                ->dehydrated(),

                            Hidden::make('lng')
                                ->dehydrated(),

                            Grid::make(3)
                                ->schema([
                                    Select::make('region_id')
                                        ->label(__('Region'))
                                        ->placeholder(__('Select Region'))
                                        ->options(function () {
                                            return \App\Models\Region::all()->mapWithKeys(function ($region) {
                                                return [$region->id => $region->name ?? 'N/A'];
                                            });
                                        })
                                        ->searchable()
                                        ->required()
                                        ->helperText(function (callable $get) {
                                            // Display the region name from map if available
                                            $regionName = $get('region');
                                            return $regionName ?
                                                new HtmlString('<span class="flex items-center gap-1 text-success-500 font-medium">'
                                                    .
                                                    view('filament::components.icon', [
                                                        'icon' => 'heroicon-o-arrow-path',
                                                        'class' => 'h-4 w-4'
                                                    ]) .  __('Map suggestion: ') . $regionName . '</span>')
                                                : null;
                                        })
                                        ->reactive()
                                        ->afterStateUpdated(function ($state, callable $set) {
                                            $set('city_id', null);
                                            $set('district_id', null);
                                        })
                                        ->validationMessages([
                                            'required' => __('Region is required'),
                                        ]),

                                    Hidden::make('region')
                                        ->label(__('Region Name from Map'))
                                        ->disabled()
                                        ->dehydrated()
                                        ->reactive()
                                        ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                            if ($state && !$get('region_id')) {
                                                // Try to find region by name
                                                $region = \App\Models\Region::whereJsonContains('name->en', $state)
                                                    ->orWhereJsonContains('name->ar', $state)
                                                    ->first();

                                                if ($region) {
                                                    $set('region_id', $region->id);
                                                }
                                            }
                                        }),

                                    Select::make('city_id')
                                        ->label(__('City'))
                                        ->placeholder(__('Select City'))
                                        ->options(function (callable $get) {
                                            $regionId = $get('region_id');
                                            if (!$regionId) return [];

                                            return \App\Models\City::where('region_id', $regionId)
                                                ->get()
                                                ->mapWithKeys(function ($city) {
                                                    return [$city->id => $city->name ?? 'N/A'];
                                                });
                                        })
                                        ->searchable()
                                        ->required()
                                        ->reactive()
                                        ->helperText(function (callable $get) {
                                            // Display the city name from map if available
                                            $cityName = $get('city');

                                            return $cityName ? new HtmlString('<span class="flex items-center gap-1 text-success-500 font-medium">'
                                                .
                                                view('filament::components.icon', [
                                                    'icon' => 'heroicon-o-arrow-path',
                                                    'class' => 'h-4 w-4'
                                                ]) .  __('Map suggestion: ') . $cityName . '</span>') : null;
                                        })
                                        ->afterStateUpdated(function ($state, callable $set) {
                                            $set('district_id', null);
                                        })
                                        ->validationMessages([
                                            'required' => __('City is required'),
                                        ]),

                                    Hidden::make('city')
                                        ->label(__('City Name from Map'))
                                        ->dehydrated()
                                        ->reactive()
                                        ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                            if ($state && $get('region_id')) {
                                                // Try to find city by name and region
                                                $cityName = $state;
                                                if (Str::contains(Str::lower($cityName), ' principality')) {
                                                    $cityName = str_ireplace(' principality', '', $cityName);
                                                }
                                                $cityName = '%'.$cityName.'%';

                                                $city = \App\Models\City::where('region_id', $get('region_id'))
                                                    ->whereRaw("LOWER(name->'$.en') LIKE ?", [strtolower($cityName)])
                                                    ->orWhereRaw("LOWER(name->'$.ar') LIKE ?", [strtolower($cityName)])
                                                    ->first();
                                                if ($city) {
                                                    $set('city_id', $city->id);
                                                }
                                            }
                                        }),

                                    Select::make('district_id')
                                        ->label(__('District'))
                                        ->placeholder(__('Select District'))
                                        ->options(function (callable $get) {
                                            $cityId = $get('city_id');
                                            if (!$cityId) return [];

                                            return \App\Models\District::where('city_id', $cityId)
                                                ->where('is_active', true)
                                                ->get()
                                                ->mapWithKeys(function ($district) {
                                                    return [$district->id => $district->name ?? 'N/A'];
                                                });
                                        })
                                        ->searchable()
                                        ->helperText(function (callable $get) {
                                            // Display the district name from map if available
                                            $districtName = $get('district');
                                            return $districtName ?
                                                new HtmlString('<span class="flex items-center gap-1 text-success-500 font-medium">'
                                                    .
                                                    view('filament::components.icon', [
                                                        'icon' => 'heroicon-o-arrow-path',
                                                        'class' => 'h-4 w-4'
                                                    ]) .  __('Map suggestion: ') . $districtName . '</span>')
                                                : null;
                                        })
                                        ->required()
                                        ->validationMessages([
                                            'required' => __('District is required'),
                                        ]),

                                    Hidden::make('district')
                                        ->label(__('District Name from Map'))
                                        ->disabled()
                                        ->dehydrated()
                                        ->reactive()
                                        ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                            if ($state && $get('city_id')) {

                                                $districtName = '%'.$state.'%';

                                                $district = \App\Models\District::where('city_id', $get('city_id'))
                                                    ->where('is_active', true)
                                                    ->whereRaw("LOWER(name->'$.en') LIKE ?", [strtolower($districtName)])
                                                    ->orWhereRaw("LOWER(name->'$.ar') LIKE ?", [strtolower($districtName)])
                                                    ->first();

                                                if ($district) {
                                                    $set('district_id', $district->id);
                                                }
                                            }
                                        }),

                                    TextInput::make('street')
                                        ->label(__('Street'))
                                        ->placeholder(__('Street'))
                                        ->required()
                                        ->dehydrated()
                                        ->validationMessages([
                                            'required' => __('Street is required')
                                        ]),

                                    TextInput::make('postal_code')
                                        ->label(__('Postal Code'))
                                        ->placeholder(__('Postal Code'))
                                        ->required()
                                        ->dehydrated()
                                        ->validationMessages([
                                            'required' => __('Postal Code is required'),
                                        ]),

                                    TextInput::make('build_number')
                                        ->label(__('Build Number'))
                                        ->placeholder(__('Build Number'))
                                        ->required()
                                        ->dehydrated()
                                        ->validationMessages([
                                            'required' => __('Build Number is required'),
                                        ]),
                                ])
                        ]),

                    Wizard\Step::make(__('Units'))
                        ->description(__('Manage property units'))
                        ->icon('heroicon-o-home')
                        ->completedIcon('heroicon-o-check-badge')
                        ->schema([
                            Repeater::make('units')
                                ->relationship('units')
                                ->label(__('Units'))
                                ->collapsed(false)
                                ->collapsible()
                                ->deletable(fn(Get $get) => count($get('units') ?? []) > 1)
                                ->itemLabel(fn (array $state): ?string =>
                                    isset($state['number']) ? __('Unit ')  . '#:' . $state['number'] : null
                                )
                                ->schema([
                                    Grid::make(3)
                                        ->schema([
                                            Hidden::make('broker_ids')
                                             ->default(fn($livewire)=> $livewire->data['broker_ids']),
                                            Hidden::make('company_id')
                                                ->default(function($livewire) {
                                                    if(!auth()->user()->hasRole(RoleEnum::ADMIN)){
                                                        return auth()->user()->company_id;
                                                    }
                                                    return $livewire->data['company_id'];
                                                }),
                                            TextInput::make('number')
                                                ->label(__('Unit Number'))
                                                ->placeholder(__('Unit Number'))
                                                ->required()
                                                ->live(true)
                                                ->validationMessages([
                                                    'required' => __('Unit Number is required'),
                                                ])
                                                ->afterStateUpdated(function ($state, $set, $get) {
                                                    // Force the repeater to refresh its items
                                                    $set('../../', $get('../../'));
                                                }),
                                            Select::make('property_type_id')
                                                ->label(__('Unit Type'))
                                                ->placeholder(__('Unit Type'))
                                                ->required()
                                                ->validationMessages([
                                                    'required' => __('Unit Type is required'),
                                                ])
                                                ->relationship(
                                                    name: 'property_type',
                                                    titleAttribute: 'name',
                                                    modifyQueryUsing: fn($query) => $query->where('property_type', PropertyTypeEnum::Unit->value)
                                                )
                                                ->searchable(['name'])
                                                ->preload()
                                                ->live()
                                                ->afterStateUpdated(function ($state, callable $set, $get) {
                                                    $set('attributes', []);

                                                    if ($state) {
                                                        $requiredAttributes = Attribute::whereHas('propertyTypes', function ($query) use ($state) {
                                                            $query->where('property_type_id', $state);
                                                        })->where('is_required', true)->get();

                                                        foreach ($requiredAttributes as $index => $attribute) {
                                                            $set("attributes.{$index}.attribute_id", $attribute->id);
                                                        }
                                                    }
                                                }),

                                            Select::make('usability_id')
                                                ->relationship(name: 'usability', titleAttribute: 'name')
                                                ->label(__('Usability'))
                                                ->placeholder(__('Usability'))
                                                ->searchable(['name'])
                                                ->preload()
                                                ->required()
                                                ->validationMessages([
                                                    'required' => __('Usability is required'),
                                                ])
                                                ->createOptionForm(fn(Form $form) => UsabilityResource::form($form))
                                                ->createOptionAction(
                                                    fn(Action $action) => $action->visible(fn() => auth()->user()->can('create', Usability::class))
                                                        ->modalWidth('2xl')
                                                ),

                                            HijriDatePicker::make('building_date')
                                                ->label(__('Building Date'))
                                                ->placeholder(__('Building Date'))
                                                ->showConvertedDate()
                                                ->syncWith('building_date')
                                                ->hideSwitcher()
                                                ->minDate(fn($get) => $get('../../building_date'))
                                                ->default(fn($get) => $get('../../building_date'))
                                                ->closeOnDateSelection()
                                                ->validationMessages([
                                                    'required' => __('Building Date is required'),
                                                ])
                                                ->native(false),
                                            TextInput::make('price')
                                                ->label(__('Price'))
                                                ->placeholder(__('Price'))
                                                ->required()
                                                ->live(true)
                                                ->debounce(700)
                                                ->numeric() // Ensures that the input is treated as a number
                                                ->rules([
                                                    'numeric',
                                                    'decimal:0,2', // Allows up to 2 decimal places
                                                    'max:99999999.99', // Maximum value for decimal(10,2)
                                                    'min:0',
                                                    'regex:/^\d{1,8}(\.\d{0,2})?$/' // Ensures max 8 digits before decimal and 2 after
                                                ])
                                                ->validationMessages([
                                                    'required' => __('Price is required'),
                                                    'max' => __('Price is required'),
                                                    'min' => __('Price is required'),
                                                ])
                                                ->placeholder(__('Enter annual unit price'))
                                                ->helperText(__('The Price should be in Annually period'))
                                                ->afterStateUpdated(function($get, $set,$state){
                                                    $pricePlans = $get('pricePlans');
                                                    foreach ($pricePlans as $key => $pricePlan) {
                                                        if($pricePlan['period_type'] == UnitPeriodType::MONTHLY){
                                                            $price = $state / 12;
                                                            $pricePlans[$key]['price'] = number_format($price, 2, '.', '');
                                                        }elseif($pricePlan['period_type'] == UnitPeriodType::HALF_ANNUALLY){
                                                            $price = $state / 2;
                                                            $pricePlans[$key]['price'] = number_format($price, 2, '.', '');
                                                        }elseif($pricePlan['period_type'] == UnitPeriodType::QUARTERLY){
                                                            $price = $state / 4;
                                                            $pricePlans[$key]['price'] = number_format($price, 2, '.', '');
                                                        }
                                                        $pricePlans[$key]['annual_price'] = $state;
                                                    }
                                                    $set('pricePlans', $pricePlans);
                                                })
                                                
                                            ]),
                                            Repeater::make('pricePlans')
                                                ->relationship('prices')
                                                ->reorderable(false)
                                                ->label(__('Price Plans'))
                                                ->columnSpanFull()
                                                ->defaultItems(0)
                                                ->collapsed(false)
                                                ->columns(2)
                                                ->grid(2)
                                                ->maxItems(3)
                                                ->collapsible(true)
                                                ->itemLabel(fn (array $state): ?string => UnitPeriodType::trans($state['period_type'] ?? null))
                                                ->schema([
                                                    Select::make('period_type')
                                                        ->label(__('Period Type'))
                                                        ->options(fn() => UnitPeriodType::repeatedLabels())
                                                        ->native(false)
                                                        ->required()
                                                        ->live()
                                                        ->disableOptionsWhenSelectedInSiblingRepeaterItems()
                                                        ->afterStateUpdated(function($state,$get,$set){
                                                            $annualPrice = $get('../../price');
                                                            if($state == UnitPeriodType::MONTHLY){
                                                                $price = $annualPrice / 12;
                                                                $set('price', number_format($price, 2, '.', ''));
                                                            }elseif($state == UnitPeriodType::HALF_ANNUALLY){
                                                                $price = $annualPrice / 2;
                                                                $set('price', number_format($price, 2, '.', ''));
                                                            }elseif($state == UnitPeriodType::QUARTERLY){
                                                                $price = $annualPrice / 4;
                                                                $set('price', number_format($price, 2, '.', ''));
                                                            }
                                                            $set('annual_price', $annualPrice);
                                                        }),
                                                    
                                                    TextInput::make('price')
                                                        ->label(__('Price'))
                                                        ->placeholder(__('Enter price'))
                                                        ->numeric()
                                                        ->required()
                                                        ->live(true)
                                                        ->debounce(700)
                                                        ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                            $period = $get('period_type');
                                                            if($period == UnitPeriodType::MONTHLY){
                                                                $annualPrice = $state * 12;
                                                            }elseif($period == UnitPeriodType::HALF_ANNUALLY){
                                                                $annualPrice = $state * 2;
                                                            }elseif($period == UnitPeriodType::QUARTERLY){
                                                                $annualPrice = $state * 4;
                                                            }
                                                            $set('annual_price', $annualPrice);
                                                        })
                                                        ->rules([
                                                            'numeric',
                                                            'decimal:0,2',
                                                            'min:0',
                                                        ])
                                                        ->helperText(function ($get){
                                                            $period = $get('period_type');
                                                            $basePrice = $get('../../price');
                                                            $annualPrice = $get('annual_price');
                                                            $diff = $annualPrice - $basePrice;
                                                            if($annualPrice){
                                                                if($period == UnitPeriodType::MONTHLY){
                                                                    return __('the difference between the Monthly price and Annually price is'). ' ' .$diff;
                                                                }elseif($period == UnitPeriodType::HALF_ANNUALLY){
                                                                    return __('the difference between the Half Annually price and Annually price is'). ' ' .$diff;
                                                                }elseif($period == UnitPeriodType::QUARTERLY){
                                                                    return __('the difference between the Quarterly price and Annually price is').' '.$diff;
                                                                }
                                                            }
                                                        })
                                                        ->validationMessages([
                                                            'numeric' => __('Static price must be a number'),
                                                            'min' => __('Static price must be greater than or equal to 0'),
                                                        ]),
                                                    Hidden::make('annual_price')
                                                        
                                                ]),
                                    Grid::make(3)
                                        ->label(__('Meters'))
                                        ->relationship('meters')
                                        ->schema([
                                            Group::make([
                                                TextInput::make('water_meter')
                                                    ->label(__('Water Meter'))
                                                    ->placeholder(__('Enter water meter number'))
                                                    ->nullable()
                                                    ->unique(
                                                        table: 'property_meters',
                                                        column: 'water_meter',
                                                        ignorable: fn ($record) => $record
                                                    )
                                                    ->dehydrated()
                                                    ->prefixIcon('heroicon-o-beaker')
                                                    ->maxLength(50)
                                                    ->extraAttributes([
                                                        'class' => 'bg-gray-50/50',
                                                    ])
                                                    ->validationMessages([
                                                        'required' => __('Water Meter is required'),
                                                        'unique' => __('This water meter number is already in use'),
                                                    ])
                                            ])->columnSpan(1),

                                            Group::make([
                                                TextInput::make('gas_meter')
                                                    ->label(__('Gas Meter'))
                                                    ->placeholder(__('Enter gas meter number'))
                                                    ->nullable()
                                                    ->unique(
                                                        table: 'property_meters',
                                                        column: 'gas_meter',
                                                        ignorable: fn ($record) => $record
                                                    )
                                                    ->dehydrated()
                                                    ->prefixIcon('heroicon-o-fire')
                                                    ->maxLength(50)
                                                    ->extraAttributes([
                                                        'class' => 'bg-gray-50/50',
                                                    ])
                                                    ->validationMessages([
                                                        'unique' => __('This Gas meter number is already in use'),
                                                    ])
                                            ])->columnSpan(1),

                                            Group::make([
                                                TextInput::make('electronic_meter')
                                                    ->label(__('Electronic Meter'))
                                                    ->placeholder(__('Enter electronic meter number'))
                                                    ->nullable()
                                                    ->unique(
                                                        table: 'property_meters',
                                                        column: 'electronic_meter',
                                                        ignorable: fn ($record) => $record
                                                    )
                                                    ->dehydrated()
                                                    ->prefixIcon('heroicon-o-bolt')
                                                    ->maxLength(50)
                                                    ->extraAttributes([
                                                        'class' => 'bg-gray-50/50',
                                                    ])
                                                    ->validationMessages([
                                                        'unique' => __('This electronic meter number is already in use'),
                                                    ])
                                            ])->columnSpan(1),
                                        ])
                                        ->columns(3)
                                        ->extraAttributes([
                                            'class' => 'mt-4 mb-4', // Add some vertical spacing
                                        ]),

                                    Repeater::make('attributes')
                                        ->label(__('attributes'))
                                        ->collapsed(false)
                                        ->collapsible()
                                        ->relationship('attributes')
                                        ->itemLabel(function (array $state): ?string {
                                            if (!isset($state['attribute_id'])) {
                                                return null;
                                            }

                                            $attribute = Attribute::find($state['attribute_id']);
                                            return $attribute ? $attribute->name : null;
                                        })
                                        ->schema([
                                            Select::make('attribute_id')
                                                ->options(function (Get $get, $livewire) {
                                                    // $propertyTypeId = $livewire->data['property_type_id'] ?? null;
                                                    $propertyTypeId = $get('../../property_type_id');
                                                    if (!$propertyTypeId) {
                                                        return [];
                                                    }
                                                    return Attribute::whereHas('propertyTypes', function ($query) use ($propertyTypeId) {
                                                        $query->where('property_types.id', $propertyTypeId);
                                                    })
                                                        ->pluck('name', 'id')
                                                        ->toArray();
                                                })
                                                ->disabled(function ($get) {
                                                    $attributeId = $get('attribute_id');
                                                    if (!$attributeId) {
                                                        return false;
                                                    }

                                                    $attribute = Attribute::find($attributeId);
                                                    return $attribute && $attribute->is_required === 1;
                                                })
                                                ->dehydrated()
                                                ->label(__('attributes'))
                                                ->placeholder(__('attributes'))
                                                ->searchable(['name'])
                                                ->preload()
                                                ->disableOptionsWhenSelectedInSiblingRepeaterItems()
                                                ->required()
                                           ->validationMessages([
                                                'required' => __('Attribute is required'),
                                            ]),
                                            TextInput::make('value')
                                                ->label(__('Value'))
                                                ->placeholder(__('Value'))
                                                ->validationMessages([
                                                    'required' => __('Value is required'),
                                                ])
                                                ->required(),


                                        ])
                                        ->deleteAction(
                                            fn(Action $action) => $action
                                                ->requiresConfirmation()
                                                ->modalHeading(__('Delete Attribute'))
                                                ->modalDescription(__('Are you sure you want to delete this attribute?'))
                                                ->before(function ($record, $state, $arguments) use ($action) {
                                                    if (isset($state[$arguments['item']]['attribute_id'])) {
                                                        $attributeId = $state[$arguments['item']]['attribute_id'];
                                                        $attribute = Attribute::find($attributeId);

                                                        if ($attribute && $attribute->is_required) {
                                                            Notification::make()
                                                                ->danger()
                                                                ->title(__('Delete Failed'))
                                                                ->body(__('This attribute is required and cannot be deleted.'))
                                                                ->send();
                                                            $action->cancel();
                                                        }
                                                    }
                                                })
                                        )
                                        ->defaultItems(0)
                                        ->columns(2),

                                        Repeater::make('vals')
                                            ->label(__('val'))
                                            ->collapsed(false)
                                            ->collapsible()
                                            ->relationship('vals')
                                            ->schema([
                                                TextInput::make('value')
                                                    ->label(__('License number'))
                                                    ->placeholder(__('License number'))
                                                    ->validationMessages([
                                                        'required' => __('License number is required'),
                                                    ])
                                                    ->required(),

                                                Hidden::make('morphable_type')
                                                    ->default(Property::class),

                                                Hidden::make('morphable_id')
                                                    ->default(function (Get $get) {
                                                        return $get('../../id');
                                                    }),

                                                HijriDatePicker::make('start_date')
                                                    ->label(__('Start Date'))
                                                    ->validationMessages([
                                                        'required' => __('Start Date is required'),
                                                    ])
                                                    ->live()
                                                    ->syncWith('vals_date')
                                                    ->showConvertedDate()
                                                    ->hideSwitcher()
                                                    ->required(),

                                                HijriDatePicker::make('end_date')
                                                    ->label(__('End Date'))
                                                    ->validationMessages([
                                                        'required' => __('End Date is required'),
                                                    ])
                                                    ->syncWith('vals_date')
                                                    ->showConvertedDate()
                                                    ->hideSwitcher()
                                                    ->minDate(fn (Get $get): string =>
                                                    $get('start_date')
                                                        ? \Carbon\Carbon::parse($get('start_date'))->addDay()->toDateString()
                                                        : now()->toDateString()
                                                    )
                                                    ->required(),

                                                Toggle::make('active')
                                                    ->label(__('Active'))
                                                    ->default(true)
                                            ])
                                            ->defaultItems(0)
                                            ->maxItems(1)
                                            ->columns(2)
                                            ->mutateRelationshipDataBeforeCreateUsing(function (array $data): array {
                                                return array_merge($data, [
                                                    'company_id' => auth()->user()->company_id,
                                                ]);
                                            }),

                                    SpatieMediaLibraryFileUpload::make('attachment')
                                        ->label(__('Attachment'))
                                        ->image()
                                        ->imageEditor()
                                        ->maxSize(5120)
                                        ->collection("property_images")
                                        ->imageEditorAspectRatios([
                                            '16:9',
                                            '4:3',
                                            '1:1',
                                        ])
                                        ->panelLayout('grid')
                                        ->reorderable()
                                        ->multiple()
                                        ->downloadable()
                                        ->openable(),
                                ])
                                ->cloneable()
                                ->cloneAction(fn (Action $action) =>
                                    $action->action(function ($livewire, $component,$set,$get,$arguments) {
                                        $items = $component->getState();
                                        $getItemUuid = $arguments['item'];


                                        $originalItem = $component->getItemState($getItemUuid);
                                        $attributes = $items[$getItemUuid]['attributes'];

                                        $clonedItem = array_merge($originalItem, [
                                            'number' => null,
                                            'attachment' => [],
                                            'attributes' => $attributes,
                                        ]);
                                        $uuid=(string)Str::uuid();

                                        $items[$uuid] = $clonedItem;
                                        $component->state($items);

                                        return $items;
                                    })
                                )
                                ->minItems(1)

                                ->columns(2),
                        ])->hidden(fn($livewire) => $livewire instanceof EditRecord)


                ])
                ->columnSpanFull()
                ->submitAction(
                    new HtmlString(
                        Blade::render(
                            <<<BLADE
                                <x-filament::button
                                    type="submit"
                                    size="sm"
                                    wire:loading.attr="disabled"
                                    wire:loading.class="opacity-70 cursor-wait"
                                    x-data
                                >
                                    <span wire:loading.remove>{{__('Submit')}}</span>
                                    <span wire:loading>{{__('Submitting...')}}</span>
                                </x-filament::button>
                            BLADE
                        )
                    )
                ),
            ]);

    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('id'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('Name'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\SpatieMediaLibraryImageColumn::make('attachment')
                    ->label(__('Property Image'))
                    ->collection('property_images')
                    ->limitedRemainingText(isSeparate: true)
                    ->limit(3)
                    ->circular()
                    ->stacked(),
                // Tables\Columns\TextColumn::make('lat')
                //     ->numeric()
                //     ->sortable(),
                // Tables\Columns\TextColumn::make('lng')
                //     ->numeric()
                //     ->sortable(),
                Tables\Columns\TextColumn::make('city')
                    ->label(__('City'))
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('district')
                    ->label(__('District'))
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('region')
                    ->label(__('Region'))
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('street')
                    ->label(__('Street'))
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('postal_code')
                    ->label(__('Postal Code'))
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('property_type.name')
                    ->label(__('Property Type'))
                    ->numeric()
                    ->sortable(),
                // Tables\Columns\TextColumn::make('parent_id')
                //     ->numeric()
                //     ->sortable(),
                // Tables\Columns\IconColumn::make('is_active')
                //     ->label(__('Is Active'))
                //     ->boolean(),
                Tables\Columns\TextColumn::make('status')
                    ->label(__('Status'))
                    ->badge()
                    ->color(fn (PropertyStatus $state): string => $state->getColor())
                    ->formatStateUsing(fn (PropertyStatus $state): string => PropertyStatus::getLabel($state))
                    ->sortable(),
                // Tables\Columns\IconColumn::make('is_available')
                //     ->label(__('Is Available'))
                //     ->boolean(),
                Tables\Columns\TextColumn::make('ejar_sync_status')
                    ->label(__('Sync with ejar'))
                    ->badge()
                    ->formatStateUsing(fn (EjarSyncStatus $state): string => EjarSyncStatus::tryFrom($state->value)->getLabel() ?? $state->value)
                    ->color(fn (EjarSyncStatus $state): string => EjarSyncStatus::tryFrom($state->value)?->getColor() ?? 'primary'),
                Tables\Columns\BooleanColumn::make('retrieved_from_ejar')
                    ->label(__('Retrieved from ejar'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('usability.name')
                    ->label(__('Usability'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created at'))
                    ->since()
                    ->tooltip(fn($record) => $record->created_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Updated at'))
                    ->since()
                    ->tooltip(fn($record) => $record->updated_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                // Tables\Filters\TrashedFilter::make(),
                Tables\Filters\SelectFilter::make('status')
                    ->label(__('Status'))
                    ->options(PropertyStatus::getOptions())
                    ->native(false)
                    ->searchable(),
                Tables\Filters\SelectFilter::make('property_type_id')
                    ->label(__('Property Type'))
                    ->options(PropertyType::select('id', 'name')->pluck('name', 'id'))
                    ->native(false)
                    ->searchable(),
                Tables\Filters\SelectFilter::make('usability_id')
                    ->label(__('Usability'))
                    ->options(Usability::select('id', 'name')->pluck('name', 'id'))
                    ->native(false)
                    ->searchable(),
                Tables\Filters\SelectFilter::make('city')
                    ->label(__('City'))
                    ->options(Property::select('city')->whereNotNull('city')->distinct()->pluck('city', 'city'))
                    ->native(false)
                    ->searchable(),
                Tables\Filters\SelectFilter::make('district')
                    ->label(__('District'))
                    ->options(Property::select('district')->whereNotNull('district')->distinct()->pluck('district', 'district'))
                    ->native(false)
                    ->searchable(),
                Tables\Filters\SelectFilter::make('region')
                    ->label(__('Region'))
                    ->options(Property::select('region')->whereNotNull('region')->distinct()->pluck('region', 'region'))
                    ->native(false)
                    ->searchable(),
                Tables\Filters\SelectFilter::make('street')
                    ->label(__('Street'))
                    ->options(Property::select('street')->whereNotNull('street')->distinct()->pluck('street', 'street'))
                    ->native(false)
                    ->searchable(),
                Tables\Filters\SelectFilter::make('postal_code')
                    ->label(__('Postal Code'))
                    ->options(Property::select('postal_code')->whereNotNull('postal_code')->distinct()->pluck('postal_code', 'postal_code'))
                    ->native(false)
                    ->searchable(),

            ])
            ->actions([
                ActionGroup::make([
                    Tables\Actions\Action::make('change_representer')
                ->label(__('Change Representer'))
                ->icon('heroicon-o-user-plus')
                ->color('warning')
                ->form([
                    Grid::make(1)
                        ->schema([
                            Section::make(__('Representer'))
                                ->label(__('Representer'))
                                ->schema([    
                                    Select::make('ownerable_id')
                                        ->label(__('Representer'))
                                        ->placeholder(__('Select representer'))
                                        ->searchable(['national_id', 'name'])
                                        ->native(false)
                                        ->required()
                                        ->live()
                                        ->afterStateUpdated(function (Get $get, $set, $state) {
                                            $set('bank_account_id', null);
                                        })
                                        ->getSearchResultsUsing(function (string $search, $record) {
                                            $accountOwnerIds = Property::find($record->id)->owners()
                                                ->where('ownerable_type', Account::class)
                                                ->pluck('ownerable_id')
                                                ->filter()
                                                ->toArray();

                                                    $leaseAccountMemberIds = Property::find($record->id)
                                                        ->propertyLeases()
                                                        ->with('allMembers')
                                                        ->get()
                                                        ->pluck('allMembers')
                                                        ->flatten()
                                                        ->where('memberable_type', Account::class)
                                                        ->whereIn('member_role', [
                                                            LeaseMemberTypesEnum::TENANT,
                                                            LeaseMemberTypesEnum::TENANT_REPRESENTER
                                                        ])
                                                        ->pluck('member_id')
                                                        ->filter()
                                                        ->toArray();

                                                    $excludeAccountIds = array_merge($accountOwnerIds, $leaseAccountMemberIds);

                                                    return Account::where(function ($query) use ($search) {
                                                        $query->where('name', '=', "{$search}")
                                                            ->orWhere('national_id', '=', "{$search}");
                                                    })
                                                        ->where('is_active', true)
                                                        ->whereNotIn('id', $excludeAccountIds)  // Exclude owner IDs
                                                        ->get()
                                                        ->mapWithKeys(function ($account) {
                                                            return [$account->id => "{$account->name} - {$account->national_id}"];
                                                        });
                                                })
                                                ->getOptionLabelUsing(function ($value) {
                                                    $account = Account::find($value);
                                                    if ($account) {
                                                        return "{$account->name} - {$account->national_id}";
                                                    }
                                                    return null;
                                                })
                                                ->debounce(500),
                                            Hidden::make('percentage')
                                                ->default(0),
                                            Hidden::make('ownerable_type')
                                                ->default(Account::class),
                                            Hidden::make('is_representer')
                                                ->default(true),
                                        ]),

                                    Section::make(__('Bank Account'))
                                        ->label(__('Bank Account'))
                                        ->schema([
                                            Select::make('bank_account_id')
                                                ->label(__('Bank Account'))
                                                ->placeholder(__('Select bank account'))
                                                ->prefixIcon('heroicon-o-credit-card')
                                                ->allowHtml()
                                                ->options(function (Get $get) {
                                                    $memberId = $get('ownerable_id');
                                                    if (!$memberId) return [];

                                                    return Account::find($memberId)?->bankAccounts()
                                                        ->where('is_active', true)
                                                        ->get()
                                                        ->mapWithKeys(function ($bankAccount) {
                                                            return [
                                                                $bankAccount->id =>  static::getBankAccountOptionView($bankAccount)
                                                            ];
                                                        });
                                                })
                                                ->getSearchResultsUsing(function (string $search, Get $get) {
                                                    $memberId = $get('ownerable_id');
                                                    if (!$memberId) return [];

                                                    return Account::find($memberId)?->bankAccounts()
                                                        ->where('is_active', true)
                                                        ->where(function ($query) use ($search) {
                                                            $query->where('bank_name', 'like', "%{$search}%")
                                                                ->orWhere('account_number', 'like', "%{$search}%")
                                                                ->orWhere('iban', 'like', "%{$search}%");
                                                        })
                                                        ->get()
                                                        ->mapWithKeys(function ($bankAccount) {
                                                            return [$bankAccount->id =>static::getBankAccountOptionView($bankAccount)];
                                                        });
                                                })
                                                ->searchable()
                                                ->debounce(500)
                                                ->required()
                                        ])->visible(function($record){
                                            $leaseIds = $record->propertyLeases()->select('leases.id')->pluck('leases.id')->toArray();
                                            if(count($leaseIds) > 0){
                                                return true;
                                            }
                                            return false;
                                        }),

                                    Section::make(__('Document'))
                                        ->relationship('document')
                                        ->schema([
                                            Grid::make(2)
                                                ->schema([
                                                    Hidden::make('document_type_id')
                                                        ->default(function () {
                                                            return DocumentType::where('key', DocumentTypeEnum::Representative->value)->first()?->id;
                                                        }),
                                                    TextInput::make('metadata.document_name_and_type')
                                                        ->label(__('Document Name and Type'))
                                                        ->placeholder(__('Document Name and Type'))
                                                        ->required(),
                                                    TextInput::make('metadata.document_number')
                                                        ->label(__('Document Number'))
                                                        ->placeholder(__('Document Number'))
                                                        ->required(),
                                                    DatePicker::make('metadata.release_date')
                                                        ->label(__('Release Date'))
                                                        ->placeholder(__('Release Date'))
                                                        ->displayFormat('d/m/Y')
                                                        ->native(false)
                                                        ->live()
                                                        ->required(),
                                                    DatePicker::make('metadata.expiration_date')
                                                        ->label(__('Expiration Date'))
                                                        ->placeholder(__('Expiration Date'))
                                                        ->displayFormat('d/m/Y')
                                                        ->native(false)
                                                        ->required()
                                                        ->disabled(fn (Get $get): bool => !$get('metadata.release_date'))
                                                        ->minDate(fn (Get $get): string =>
                                                        $get('metadata.release_date')
                                                            ? \Carbon\Carbon::parse($get('release_date'))->addDay()->toDateString()
                                                            : now()->toDateString()
                                                        ),
                                                    TextInput::make('metadata.released_by')
                                                        ->label(__('Released By'))
                                                        ->placeholder(__('Released By'))
                                                        ->required(),
                                                    TextInput::make('metadata.released_in')
                                                        ->label(__('Released In'))
                                                        ->placeholder(__('Released In'))
                                                        ->required(),
                                                    SpatieMediaLibraryFileUpload::make('upload_contract')
                                                        ->label(__('Upload Contract Authorization'))
                                                        ->downloadable()
                                                        ->openable()
                                                        ->panelLayout('grid')
                                                        ->reorderable()
                                                        ->multiple()
                                                        ->maxSize(5120)
                                                        ->collection('upload_contract')
                                                        ->imageEditorAspectRatios([
                                                            '16:9',
                                                            '4:3',
                                                            '1:1',
                                                        ])
                                                        ->columnSpanFull()
                                                        ->required(),
                                                ])
                                        ]),
                                ]),
                        ])
                        ->action(function (array $data, $record): void {
                            try {
                                $record->onlyOwner()->where('is_representer', true)->update(['is_representer' => false]);
                                $record->onlyRepresentative()->delete();
                                $record->onlyRepresentative()->create([
                                    'ownerable_id' => $data['ownerable_id'],
                                    'ownerable_type' => $data['ownerable_type'],
                                    'percentage' => $data['percentage'],
                                    'is_representer' => $data['is_representer'],
                                ]);
                                $leaseIds = $record->propertyLeases()->select('leases.id')->pluck('leases.id')->toArray();

                                if (!empty($leaseIds)) {
                                    LeaseMember::whereIn('lease_id', $leaseIds)
                                        ->where('member_role', LeaseMemberTypesEnum::LESSOR_REPRESENTER)
                                        ->update([
                                            'member_id' => $record->onlyRepresentative->ownerable_id,
                                            'bank_account_id' => $data['bank_account_id'],
                                        ]);
                                }



                        Notification::make()
                            ->title(__('Representer changed successfully'))
                            ->success()
                            ->send();
                            
                    } catch (\Exception $e) {
                        
                        Notification::make()
                            ->title(__('Error while changing representer'))
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                })
                ->visible(fn ($record): bool => 
                    auth()->user()->can('update', $record) && 
                    $record->owners()->where('is_representer', true)->exists()
                ),
                Tables\Actions\DeleteAction::make()
                        ->visible(fn (Property $record): bool => $record->status === PropertyStatus::DRAFT),
                    Tables\Actions\EditAction::make()
                        ->visible(fn (Property $record): bool => $record->status === PropertyStatus::DRAFT || $record->status === PropertyStatus::ACTIVE),
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\Action::make('publish')
                        ->label(__('Publish'))
                        ->icon('heroicon-o-paper-airplane')
                        ->color('success')
                        ->requiresConfirmation()
                        ->modalHeading(__('Publish Property'))
                        ->modalDescription(__('Are you sure you want to publish this property?'))
                        ->modalSubmitActionLabel(__('Yes, publish it'))
                        ->action(function (Property $record): void {
                            $record->update([
                                'status' => PropertyStatus::ACTIVE
                            ]);

                            Notification::make()
                                ->title(__('Property published successfully'))
                                ->success()
                                ->send();
                        })
                        ->visible(fn (Property $record): bool => $record->status === PropertyStatus::DRAFT),
                ])
                    ->button()
                    ->extraAttributes([
                        'class' => 'custom-action-btn',
                    ])
                    ->color('transparent')
                    ->label(__('Commends')),

            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                //     Tables\Actions\ForceDeleteBulkAction::make(),
                //     Tables\Actions\RestoreBulkAction::make(),
                // ]),
            ]);
    }

    public static function getBankAccountOptionView(Model $model): string
    {
        return view('lease::forms.components.bank-account-selecet',['bankAccount' => $model])->render();

    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                InfolistView::make('property::view.propertyDetails')
                    ->viewData([
                            'record' => $infolist->record,
                            'document' => $infolist->record->documentOwnership,
                            'propertyType' => $infolist->record->property_type,
                            'meters' => $infolist->record->meters,
                            'usability'=> $infolist->record->usability,
                            'unitCount' => $infolist->record->units()->count(),
                        ])
                    ->columnSpanFull(),
                CustomInfoTabs::make('property Information')
                    ->tabs([
                        Tab::make(__('Document'))
                            ->icon('heroicon-o-banknotes')
                            ->schema([
                                InfolistSection::make(__('Ownership Document'))
                                    ->collapsed()
                                    ->schema([
                                        InfolistGrid::make(['default' => 1, 'sm' => 2])
                                            ->schema([
                                                TextEntry::make('documentOwnership.metadata.document_type')
                                                    ->label(__('Document Type')),

                                                TextEntry::make('documentOwnership.metadata.owner_id_number')
                                                    ->label(__('Owner ID Number'))
                                                    ->visible(fn ($record) => $record->documentOwnership?->metadata['document_type'] === OwnershipDocumentTypeEnum::Electronic->value),

                                                TextEntry::make('documentOwnership.metadata.ownership_reference_no')
                                                    ->label(__('Ownership Reference No.'))
                                                    ->visible(fn ($record) => in_array($record->documentOwnership?->metadata['document_type'], [
                                                        OwnershipDocumentTypeEnum::Electronic->value,
                                                        OwnershipDocumentTypeEnum::PaperTitleDeed->value,
                                                        OwnershipDocumentTypeEnum::HojjatEsthkam->value,
                                                        OwnershipDocumentTypeEnum::Other->value,
                                                    ])),

                                                TextEntry::make('documentOwnership.metadata.real_estate_number')
                                                    ->label(__('Real Estate Number'))
                                                    ->visible(fn ($record) => $record->documentOwnership?->metadata['document_type'] === OwnershipDocumentTypeEnum::RealEstateRegistryTitleDeed->value),

                                                TextEntry::make('documentOwnership.metadata.legal_document_type_name')
                                                    ->label(__('Legal Document Type Name'))
                                                    ->visible(fn ($record) => $record->documentOwnership?->metadata['document_type'] === OwnershipDocumentTypeEnum::Other->value),

                                                TextEntry::make('documentOwnership.metadata.issue_date')
                                                    ->label(fn ($record) => $record->documentOwnership?->metadata['document_type'] === OwnershipDocumentTypeEnum::RealEstateRegistryTitleDeed->value
                                                        ? __('First Registration Date')
                                                        : __('Issue Date'))
                                                    ->visible(fn ($record) => in_array($record->documentOwnership?->metadata['document_type'], [
                                                        OwnershipDocumentTypeEnum::Electronic->value,
                                                        OwnershipDocumentTypeEnum::PaperTitleDeed->value,
                                                        OwnershipDocumentTypeEnum::HojjatEsthkam->value,
                                                        OwnershipDocumentTypeEnum::RealEstateRegistryTitleDeed->value,
                                                        OwnershipDocumentTypeEnum::Other->value,
                                                    ])),

                                                CustomSwiperImageEntry::make('documentOwnership')
                                                    ->label(__('Upload Contract Authorization'))
                                                    ->columnSpanFull()
                                                    ->spatie()
                                                    ->collection('ownership_document')
                                                    ->navigation(true)
                                                    ->pagination()
                                                    ->paginationType(CustomSwiperImageEntry::BULLETS)
                                                    ->paginationClickable()
                                                    ->paginationDynamicBullets()
                                                    ->paginationHideOnClick()
                                                    ->paginationDynamicMainBullets(2)
                                                    // ->scrollbar()
                                                    // ->scrollbarDragSize(100)
                                                    // ->scrollbarDraggable()
                                                    // ->scrollbarSnapOnRelease()
                                                    // ->scrollbarHide(false)
                                                    ->height(300)
                                                    ->autoplay()
                                                    // ->effect(CustomSwiperImageEntry::CARDS_EFFECT)
                                                    // ->cardsPerSlideOffset(2)
                                                    ->autoplayDelay(2000)
                                                    ->centeredSlides()
                                                    ->slidesPerView(2)
                                            ]),
                                    ]),

                                    InfolistSection::make(__('Representative Document'))
                                    ->collapsed()
                                    ->schema([
                                        InfolistGrid::make(['default' => 1, 'sm' => 2])
                                            ->schema([
                                                TextEntry::make('document.metadata.document_name_and_type')
                                                    ->label(__('Document Name and Type')),
                                                TextEntry::make('document.metadata.document_number')
                                                    ->label(__('Document Number')),
                                                TextEntry::make('document.metadata.release_date')
                                                    ->label(__('Release Date')),
                                                TextEntry::make('document.metadata.expiration_date')
                                                    ->label(__('Expiration Date')),
                                                TextEntry::make('document.metadata.released_by')
                                                    ->label(__('Released By')),
                                                TextEntry::make('document.metadata.released_in')
                                                    ->label(__('Released In')),
                                                CustomSwiperImageEntry::make('document')
                                                    ->label(__('Upload Contract Authorization'))
                                                    ->columnSpanFull()
                                                    ->spatie()
                                                    ->collection('upload_contract')
                                                    ->navigation(true)
                                                    ->pagination()
                                                    ->paginationType(CustomSwiperImageEntry::BULLETS)
                                                    ->paginationClickable()
                                                    ->paginationDynamicBullets()
                                                    ->paginationHideOnClick()
                                                    ->paginationDynamicMainBullets(2)
                                                    // ->scrollbar()
                                                    // ->scrollbarDragSize(100)
                                                    // ->scrollbarDraggable()
                                                    // ->scrollbarSnapOnRelease()
                                                    // ->scrollbarHide(false)
                                                    ->height(300)
                                                    ->autoplay()
                                                    // ->effect(CustomSwiperImageEntry::CARDS_EFFECT)
                                                    // ->cardsPerSlideOffset(2)
                                                    ->autoplayDelay(2000)
                                                    ->centeredSlides()
                                                    ->slidesPerView(2)
                                            ]),
                                    ])->visible(fn ($record) => $record->document !== null),
                            ]),
                        Tab::make(__('Owners'))
                            ->icon('heroicon-o-user-group')
                            ->schema([
                                InfolistSection::make(__('Company & Broker'))
                                    ->collapsed()
                                    ->schema([
                                        InfolistGrid::make(['default' => 1, 'sm' => 2])
                                            ->schema([
                                                TextEntry::make('company.name')
                                                    ->label(__('Company')),
                                                TextEntry::make('brokers.name')
                                                    ->label(__('Brokers'))
                                                    ->listWithLineBreaks()
                                                    ->visible(fn ($record) => $record->brokers->isNotEmpty()),
                                            ]),
                                    ]),

                                InfolistSection::make(__('Owners'))
                                    ->collapsed()
                                    ->schema([
                                        InfolistView::make('property::view.propertyMembers')
                                            ->viewData(['members' => $infolist->record->viewOwners]),
                                    ]),

                            ]),
                        Tab::make(__('Property Details'))
                        ->icon('heroicon-o-building-office-2')
                        ->schema([
                            InfolistSection::make(__('Property Details'))
                                ->schema([
                                    InfolistGrid::make(['default' => 1, 'sm' => 2])
                                        ->schema([
                                            TextEntry::make('building_date')
                                                ->label(__('Building Date')),
                                            TextEntry::make('amenities.name')
                                                ->label(__('Amenities'))
                                                ->badge(),
                                            TextEntry::make('property_type.name')
                                                ->label(__('Property Type')),
                                            TextEntry::make('usability.name')
                                                ->label(__('Usability')),
                                            TextEntry::make('attributes')
                                                ->label(__('Attributes'))
                                                ->getStateUsing(function ($record) {
                                                    return $record->attributes->map(function ($propertyAttribute) {
                                                        return $propertyAttribute->attribute->name . ': ' . $propertyAttribute->value;
                                                    })->join(', ');
                                                })
                                                ->badge(),
                                            CustomSwiperImageEntry::make('attachment')
                                                ->label(__('Attachment'))
                                                ->columnSpanFull()
                                                ->spatie()
                                                ->collection('property_images')
                                                ->navigation(true)
                                                ->pagination()
                                                ->paginationType(CustomSwiperImageEntry::BULLETS)
                                                ->paginationClickable()
                                                ->paginationDynamicBullets()
                                                ->paginationHideOnClick()
                                                ->paginationDynamicMainBullets(2)
                                                // ->scrollbar()
                                                // ->scrollbarDragSize(100)
                                                // ->scrollbarDraggable()
                                                // ->scrollbarSnapOnRelease()
                                                // ->scrollbarHide(false)
                                                ->height(300)
                                                ->autoplay()
                                                // ->effect(CustomSwiperImageEntry::CARDS_EFFECT)
                                                // ->cardsPerSlideOffset(2)
                                                ->autoplayDelay(2000)
                                                ->centeredSlides()
                                                ->slidesPerView(2)
                                                ->setParent(true),
                                            TextEntry::make('meters.gas_meter')
                                            ->label(__('Gas Meter')),
                                            TextEntry::make('meters.water_meter')
                                            ->label(__('Water Meter')),
                                            TextEntry::make('meters.electronic_meter')
                                            ->label(__('Electronic Meter')),
                                        ]),
                                ]),
                        ]),
                        Tab::make(__('Property Address'))
                            ->icon('heroicon-o-map-pin')
                            ->schema([
                                InfolistSection::make(__('Property Address'))
                                    ->schema([
                                        InfolistGrid::make(['default' => 1, 'sm' => 2])
                                            ->schema([
                                                TextEntry::make('street')
                                                    ->label(__('Street')),
                                                TextEntry::make('region')
                                                    ->label(__('Region')),
                                                TextEntry::make('city')
                                                    ->label(__('City')),
                                                TextEntry::make('district')
                                                    ->label(__('District')),
                                                TextEntry::make('postal_code')
                                                    ->label(__('Postal Code')),
                                                TextEntry::make('build_number')
                                                    ->label(__('Building Number')),
                                            ]),
                                    ]),
                            ]),

                    ])
                    ->columnSpanFull(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            UnitsRelationManager::class,
            BrokersLogRelationManager::class,
            // AmenitiesRelationManager::class,
            // OwnersRelationManager::class,
            SyncPropertyStepsRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => PropertyResource\Pages\ListProperties::route('/'),
            'create' => PropertyResource\Pages\CreateProperty::route('/create'),
            'edit' => PropertyResource\Pages\EditProperty::route('/{record}/edit'),
            'view' => PropertyResource\Pages\ViewProperty::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();


        $query->withoutGlobalScopes([
                SoftDeletingScope::class,
            ])
            ->whereNull('parent_id');
        return $query;
    }

    public static function getWidgets(): array
    {
        return [
            PropertyListOverview::class
        ];
    }

    public static function getPermissionPrefixes(): array
    {
        return [
            'view',
            'view_any',
            'create',
            'update',
            'delete',
            'delete_any',
            'force_delete',
            'force_delete_any',
            'restore',
            'restore_any',
            'replicate',
            'reorder',
            'sync',
            'view_sync',
            'edit_broker_field',
        ];
    }

    // public static function getNavigationGroup(): ?string
    // {
    //     return __("property management");
    // }
    public static function getNavigationLabel(): string
    {
        return __("properties");
    }

    public static function getNavigationGroup(): string
    {
        return __('Properties Management');
    }

    public static function getBreadcrumb() : string
    {
        return __('Property');
    }
    public static function getModelLabel(): string
    {
        return __('Property');
    }

    public static function getPluralModelLabel(): string
    {
        return __('properties');
    }
}
