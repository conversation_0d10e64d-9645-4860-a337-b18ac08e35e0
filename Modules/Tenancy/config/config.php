<?php

use App\Models\Document;
use App\Models\User;
use App\Models\Val;
use Modules\MaintenanceRequest\app\Models\MaintenanceRequest;
use Modules\Property\app\Models\Property;
use Spatie\Permission\Models\Role;
use Modules\Lease\app\Models\Lease;
use Modules\Invoice\app\Models\Invoice;
use Modules\Invoice\app\Models\InvoiceItem;
return [
    'name' => 'Tenancy',

    'company_tables' => [
        'users',
        'roles',
        'permissions',
        'properties',
        'leases',
        'maintenance_requests',
        'vals',
        'leases',
        'documents',
        'invoices',
    ],
    'models'=>[
        User::class,
        Role::class,
        Property::class ,
        MaintenanceRequest::class,
        Val::class,
        Lease::class,
        Invoice::class,
        Document::class,
    ],


    'modelsBroker' => [
        Property::class => [
            'relation' => 'brokers',
            'column' => 'broker_id'
        ],
        Lease::class => [
            'relation' => 'property.brokers',
            'column' => 'broker_id'
        ],
        Document::class => [
            'relation' => 'morphable',
            'morph' => [
                Property::class => [
                    'relation' => 'brokers',
                    'column' => 'broker_id'
                ],
                Lease::class => [
                    'relation' => 'property.brokers',
                    'column' => 'broker_id'
                ]
        ],
        Invoice::class => [
            'paths'=>[
                'relation' => 'items',
                'morph' => [
                    'itemable' => [
                        Lease::class => [
                            'relation' => 'property.brokers',
                            'column' => 'broker_id'
                        ],
                    ]
                ]
            ]
        ],
    // For models with direct broker_id column
//        Property::class => null,

//        \App\Models\Contract::class => [
//            'relation' => 'unit',
//            'nested' => [
//                'relation' => 'property',
//                'column' => 'broker_id'
//            ]
//        ],

//
//        // For models with multiple paths
//        \App\Models\ComplexModel::class => [
//            'paths' => [
//                [
//                    'relation' => 'property',
//                    'column' => 'broker_id'
//                ],
//                [
//                    'relation' => 'agent',
//                    'column' => 'broker_id'
//                ]
//            ]
//        ]
    ],

        // Default configuration
        'default' => [
            'column' => 'broker_id'
        ]
    ]
];
