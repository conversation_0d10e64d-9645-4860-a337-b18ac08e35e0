<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Modules\Invoice\app\Models\Invoice;
use Modules\Invoice\Enums\InvoiceStatusEnum;
use Modules\Invoice\Enums\InvoiceTypeEnum;
use Illuminate\Support\HtmlString;

class LatestInvoicesWidget extends BaseWidget
{
    protected static ?string $heading = 'Latest Invoices';

    protected int | string | array $columnSpan = 1;

    public function table(Table $table): Table
    {
        // Get exactly 15 latest invoices
        $invoices = Invoice::query()
            ->latest()
            ->limit(15)
            ->get();

        return $table
            ->query(
                Invoice::query()
                    ->whereIn('id', $invoices->pluck('id'))
                    ->latest()
            )
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('uuid')
                    ->label('Invoice #')
                    ->limit(15)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 15) {
                            return null;
                        }
                        return $state;
                    }),
                Tables\Columns\TextColumn::make('total')
                    ->label('Total')
                    ->formatStateUsing(function ($state) {
                        return new HtmlString(
                            '<div class="">' .
                            number_format($state, 2) .
                            ' <span class="text-xs">SAR</span>' .
                            '</div>'
                        );
                    }),
                Tables\Columns\TextColumn::make('paid')
                    ->label('Paid')
                    ->formatStateUsing(function ($state) {
                        return new HtmlString(
                            '<div class="">' .
                            number_format($state, 2) .
                            ' <span class="text-xs">SAR</span>' .
                            '</div>'
                        );
                    }),
                Tables\Columns\TextColumn::make('remaining')
                    ->label('Remaining')
                    ->formatStateUsing(function ($state) {
                        return new HtmlString(
                            '<div class="">' .
                            number_format($state, 2) .
                            ' <span class="text-xs">SAR</span>' .
                            '</div>'
                        );
                    }),
                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'paid' => 'success',
                        'unpaid' => 'danger',
                        'partial_paid' => 'warning',
                        'settled' => 'info',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'unpaid' => 'Unpaid',
                        'partial_paid' => 'Partially Paid',
                        'paid' => 'Paid',
                        'settled' => 'Settled',
                        default => ucfirst(str_replace('_', ' ', $state)),
                    }),
                Tables\Columns\TextColumn::make('due_date')
                    ->label('Due Date')
                    ->date('d-m-Y')
                    ->color(fn ($record): string =>
                        ($record->due_date && $record->due_date < now() && $record->remaining > 0)
                            ? 'danger'
                            : 'gray'
                    ),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'unpaid' => 'Unpaid',
                        'partial_paid' => 'Partially Paid',
                        'paid' => 'Paid',
                        'settled' => 'Settled',
                    ])
                    ->placeholder('All Statuses'),
            ])
            ->actions([
                Tables\Actions\Action::make('view')
                    ->label('View')
                    ->icon('heroicon-m-eye')
                    ->url(fn (Invoice $record): string => \Modules\Invoice\app\Filament\Resources\InvoiceResource::getUrl('view', ['record' => $record]))
                    ->openUrlInNewTab(),
            ])
            ->headerActions([
                Tables\Actions\Action::make('view_all')
                    ->label('View All Invoices')
                    ->icon('heroicon-m-arrow-top-right-on-square')
                    ->url(\Modules\Invoice\app\Filament\Resources\InvoiceResource::getUrl('index'))
                    ->color('primary'),
            ])
            ->paginated([5, 10, 15])
            ->defaultPaginationPageOption(5);
    }
}
