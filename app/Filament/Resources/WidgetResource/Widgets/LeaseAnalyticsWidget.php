<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use Filament\Widgets\Widget;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\Enums\LeaseEnum;
use Modules\Lease\Enums\LeaseTypesEnum;
use Filament\Forms\Components\Select;

class LeaseAnalyticsWidget extends Widget
{
    protected static ?string $heading = 'Lease Analytics';

    protected int | string | array $columnSpan = 1;

    protected static ?int $sort = 2;

    public ?string $filter = 'status';
    public string $chartType = 'bar';

    protected static string $view = 'filament.widgets.lease-analytics-widget';

    protected static ?string $pollingInterval = null;



    protected function getData(): array
    {
        $activeFilter = $this->filter;

        switch ($activeFilter) {
            case 'status':
                return $this->getStatusData();
            case 'lease_type':
                return $this->getLeaseTypeData();
            case 'status_and_type':
                return $this->getStatusAndTypeData();
            default:
                return $this->getStatusData();
        }
    }

    protected function getStatusData(): array
    {
        $leases = Lease::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        $labels = [];
        $data = [];
        $colors = [];

        foreach ($leases as $status => $count) {
            $labels[] = LeaseEnum::trans($status);
            $data[] = $count;
            $colors[] = $this->getStatusColor($status);
        }

        return [
            'datasets' => [
                [
                    'label' => 'Leases by Status',
                    'data' => $data,
                    'backgroundColor' => $colors,
                    'borderColor' => $colors,
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getLeaseTypeData(): array
    {
        $leases = Lease::selectRaw('lease_type, COUNT(*) as count')
            ->groupBy('lease_type')
            ->get();

        $labels = [];
        $data = [];
        $colors = ['#10B981', '#3B82F6', '#F59E0B', '#EF4444']; // Green, Blue, Yellow, Red

        foreach ($leases as $lease) {
            $labels[] = $this->getLeaseTypeLabel($lease->lease_type);
            $data[] = $lease->count;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Leases by Type',
                    'data' => $data,
                    'backgroundColor' => array_slice($colors, 0, count($data)),
                    'borderColor' => array_slice($colors, 0, count($data)),
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getStatusAndTypeData(): array
    {
        // Get status data
        $statusLeases = Lease::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get();

        // Get type data
        $typeLeases = Lease::selectRaw('lease_type, COUNT(*) as count')
            ->groupBy('lease_type')
            ->get();

        $statusLabels = [];
        $statusData = [];
        $statusColors = [];

        foreach ($statusLeases as $lease) {
            $statusLabels[] = LeaseEnum::trans($lease->status);
            $statusData[] = $lease->count;
            $statusColors[] = $this->getStatusColor($lease->status);
        }

        $typeLabels = [];
        $typeData = [];
        $typeColors = ['#10B981', '#3B82F6'];

        foreach ($typeLeases as $lease) {
            $typeLabels[] = $this->getLeaseTypeLabel($lease->lease_type);
            $typeData[] = $lease->count;
        }

        return [
            'datasets' => [
                [
                    'label' => 'By Status',
                    'data' => $statusData,
                    'backgroundColor' => $statusColors,
                    'borderColor' => $statusColors,
                    'borderWidth' => 2,
                ],
                [
                    'label' => 'By Type',
                    'data' => $typeData,
                    'backgroundColor' => array_slice($typeColors, 0, count($typeData)),
                    'borderColor' => array_slice($typeColors, 0, count($typeData)),
                    'borderWidth' => 2,
                ],
            ],
            'labels' => array_merge($statusLabels, $typeLabels),
        ];
    }



    protected function getStatusColor(string $status): string
    {
        return match($status) {
            'draft' => '#6B7280',           // Gray
            'published' => '#10B981',       // Green
            'reserved' => '#3B82F6',        // Blue
            'near_to_expire' => '#EF4444',  // Red
            'terminated' => '#DC2626',      // Dark Red
            'terminate_request' => '#F59E0B', // Yellow
            'closed' => '#6366F1',          // Indigo
            'close_request' => '#F97316',   // Orange
            'ended' => '#8B5CF6',           // Purple
            'renewed' => '#06B6D4',         // Cyan
            default => '#9CA3AF',           // Light Gray
        };
    }

    public function getOptions(): array
    {
        $baseOptions = [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'title' => [
                    'display' => true,
                    'text' => $this->getChartTitle(),
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];

        if ($this->chartType === 'bar') {
            $baseOptions['scales'] = [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'stepSize' => 1,
                    ],
                ],
            ];
        }

        return $baseOptions;
    }

    protected function getChartTitle(): string
    {
        return match($this->filter) {
            'status' => 'Leases Distribution by Status',
            'lease_type' => 'Leases Distribution by Type',
            'status_and_type' => 'Leases by Status and Type',
            default => 'Lease Analytics',
        };
    }

    protected function getLeaseTypeLabel(string $type): string
    {
        return match($type) {
            'residential' => 'Residential',
            'commercial' => 'Commercial',
            default => ucfirst($type),
        };
    }

    public function updateChartData(): void
    {
        // This will trigger a re-render of the chart
        $this->dispatch('refreshChart');
    }

    public function changeChartType(string $type): void
    {
        $this->chartType = $type;
        $this->dispatch('refreshLeaseChart');
    }

    public function updatedFilter(): void
    {
        $this->dispatch('refreshLeaseChart');
    }

    public function getCachedData(): array
    {
        return $this->getData();
    }

    public function getHeading(): string
    {
        return static::$heading ?? 'Lease Analytics';
    }
}
