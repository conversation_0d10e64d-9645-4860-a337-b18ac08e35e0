<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use <PERSON>zhan<PERSON>alleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Modules\Invoice\app\Models\Invoice;
use App\Shared\CalculatesDailyStats;

class TotalInvoicesCount extends BaseWidget
{
    use HasWidgetShield, CalculatesDailyStats;

    protected int | string | array $columnSpan = '1';

    protected function getColumns(): int
    {
        return $this->columnSpan;
    }

    protected function getHeading(): ?string
    {
        return __('Total Invoices');
    }

    protected function getStats(): array
    {
        $totalInvoices = Invoice::select('id', 'updated_at')->get();
        $invoicesUrl = url('/') . '/' . 'admin' . '/' . 'invoices';
        $invoicesChart = $this->calculateDailyStats($totalInvoices, 'updated_at');

        $totalInvoicesCount = $totalInvoices->count();

        return [
            Stat::make(__('Total Invoices'), $totalInvoicesCount)
                ->color('info')
                ->url($invoicesUrl)
                ->chart([1,1])
                ->icon('heroicon-o-document-text')
        ];
    }
}
