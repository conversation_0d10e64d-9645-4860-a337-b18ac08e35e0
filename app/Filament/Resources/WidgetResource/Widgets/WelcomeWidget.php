<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use Filament\Widgets\Widget;

class WelcomeWidget extends Widget
{
    protected static string $view = 'filament.widgets.welcome-widget';

    protected int | string | array $columnSpan = 'full';

    protected static ?int $sort = -10; // Display at the top

    public function getColumnSpan(): int | string | array
    {
        return $this->columnSpan;
    }

    public static function canView(): bool
    {
        return true;
    }
}
