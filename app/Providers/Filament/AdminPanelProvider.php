<?php

namespace App\Providers\Filament;

use App\Filament\Pages\Auth\EmailVerification\EmailVerificationPrompt;
use App\Filament\Pages\CustomResetPassword;
use App\Filament\Pages\CustomResetPasswordFrom;
use App\Filament\Pages\Login;
use App\Filament\Pages\Register;
use App\Filament\Resources\WidgetResource\Widgets\ActiveLeaseCount;
use App\Filament\Resources\WidgetResource\Widgets\ActiveMaintenanceRequestsCount;
use App\Filament\Resources\WidgetResource\Widgets\ActiveSubscription;
use App\Filament\Resources\WidgetResource\Widgets\CommissionInvoicesTotal;
use App\Filament\Resources\WidgetResource\Widgets\CompanyCount;
use App\Filament\Resources\WidgetResource\Widgets\EjarRegisteredLeaseCount;
use App\Filament\Resources\WidgetResource\Widgets\InactiveLeaseCount;
use App\Filament\Resources\WidgetResource\Widgets\InactiveMaintenanceRequestsCount;
use App\Filament\Resources\WidgetResource\Widgets\InactiveSubscription;
use App\Filament\Resources\WidgetResource\Widgets\LeaseChart;
use App\Filament\Resources\WidgetResource\Widgets\ProperitiesManagedCount;
use App\Filament\Resources\WidgetResource\Widgets\TotalPaidPayments;
use App\Filament\Resources\WidgetResource\Widgets\TotalPaymentIncome;
use App\Filament\Resources\WidgetResource\Widgets\TotalRentCurrentMonth;
use App\Filament\Resources\WidgetResource\Widgets\TotalSubscriptionIncome;
use App\Filament\Resources\WidgetResource\Widgets\TotalSubscriptionUpcome;
use App\Filament\Resources\WidgetResource\Widgets\TotalUnpaidPayments;
use App\Filament\Widgets\WelcomeWidget;
use App\Filament\Widgets\LatestLeasesWidget;
use App\Filament\Widgets\LatestInvoicesWidget;
use App\Filament\Widgets\LeaseAnalyticsWidget;
use App\Filament\Widgets\InvoiceAnalyticsWidget;
use App\Filament\Widgets\PropertyAnalyticsWidget;
use App\Http\Middleware\CheckPermissionMiddleware;
use App\Shared\Injection;
use CharrafiMed\GlobalSearchModal\GlobalSearchModalPlugin;
use Filament\Http\Middleware\Authenticate;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Enums\MaxWidth;
use Modules\Property\app\Filament\PropertyPlugin;
use Modules\Subscription\app\Filament\Resources\PlanResource\pages\SubscriptionPlans;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->login(Login::class)
            ->registration(Register::class)
            ->passwordReset(CustomResetPassword::class, CustomResetPasswordFrom::class)
            ->emailVerification(EmailVerificationPrompt::class)
            ->profile(isSimple: false)
            ->default()
            ->id('admin')
            ->path('admin')
            ->renderHook(
                'panels::footer',
                fn (): string => view('filament.footer')->render()
            )
            ->colors([
                'primary' => '#246250',
                // 'gray' => Color::Gray,
            ])
            ->favicon(asset('images/Favicon.png'))
            ->brandLogo(asset('images/Kera_light_logo.png'))
            // ->darkModeBrandLogo(asset('images/keraNewLogo.png'))
            ->brandLogoHeight('50px')
            ->sidebarCollapsibleOnDesktop()
            ->maxContentWidth(MaxWidth::Full)
            ->plugins(Injection::getPlugins())
            ->navigationGroups(Injection::getGroups())
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                \App\Filament\Pages\Dashboard::class,
                SubscriptionPlans::class,

            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                WelcomeWidget::class,
                CompanyCount::class,
                ActiveLeaseCount::class,
                InactiveLeaseCount::class,
                EjarRegisteredLeaseCount::class,
                ActiveMaintenanceRequestsCount::class,
                InactiveMaintenanceRequestsCount::class,
                CommissionInvoicesTotal::class,
                TotalPaymentIncome::class,
                TotalPaidPayments::class,
                InactiveSubscription::class,
                ActiveSubscription::class,
                TotalSubscriptionIncome::class,
                TotalSubscriptionUpcome::class,
                ProperitiesManagedCount::class,
                TotalUnpaidPayments::class,
                TotalRentCurrentMonth::class,
                LeaseChart::class,
                LeaseAnalyticsWidget::class,
                InvoiceAnalyticsWidget::class,
                PropertyAnalyticsWidget::class,
                LatestLeasesWidget::class,
                LatestInvoicesWidget::class,
            ])
            ->authMiddleware([
                Authenticate::class,
                \Modules\Subscription\app\Http\Middleware\Subscription::class,
                \App\Http\Middleware\CheckIfUserIsSuspended::class,
                // \App\Http\Middleware\CheckIfUserNafathVerified::class,
                CheckPermissionMiddleware::class
            ])
            ->middleware(Injection::getMiddleware())
            ->viteTheme(['resources/css/customTheme.css','public/app/Forms/Components/datepicker/css/bootstrap-datetimepicker.css'])
            ->globalSearchKeyBindings(['command+k', 'ctrl+k'])
            ->globalSearchDebounce('750ms')
            ->globalSearchFieldKeyBindingSuffix()
            ->spa()
            ->databaseNotifications()
            ->renderHook(
                'panels::head.start',
                fn (): string => '<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@emran-alhaddad/saudi-riyal-font/index.css">'
            )
            ->plugin(PropertyPlugin::make())
            ;
    }
}
