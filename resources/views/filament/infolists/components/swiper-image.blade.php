@php
    $limit = $getLimit();
    $state = \Illuminate\Support\Arr::wrap($getState());
    $limitedState = array_slice($state, 0, $limit);
    $isCircular = $isCircular();
    $isSquare = $isSquare();
    $isStacked = $isStacked();
    $overlap = $isStacked ? $getOverlap() ?? 2 : null;
    $ring = $isStacked ? $getRing() ?? 2 : null;
    $height = $getHeight() ?? ($isStacked ? '2.5rem' : '8rem');
    $width = $getWidth() ?? ($isCircular || $isSquare ? $height : null);
    $defaultImageUrl = $getDefaultImageUrl();
    $pagination = $getPagination();
    $navigation = $getNavigation();
    $slidesPerView = $getSlidesPerView();
    $paginationType = $getPaginationType();
    $autoplay = $getAutoplay();
    $autoplayDelay = $getAutoplayDelay();
    $paginationClickable = $getPaginationClickable();
    $paginationDynamicBullets = $getPaginationDynamicBullets();
    $paginationDynamicMainBullets = $getPaginationDynamicMainBullets();
    $paginationHideOnClick = $getPaginationHideOnClick();

    $scrollbar = $getScrollbar();
    $scrollbarDraggable = $getScrollbarDraggable();
    $scrollbarHide = $getScrollbarHide();
    $scrollbarSnapOnRelease = $getScrollbarSnapOnRelease();
    $scrollbarDragSize = $getScrollbarDragSize();

    $effect = $getEffect();

    if (!count($limitedState) && filled($defaultImageUrl)) {
        $limitedState = [null];
    }

    $ringClasses = \Illuminate\Support\Arr::toCssClasses([
        'ring-white dark:ring-gray-900',
        match ($ring) {
            0 => null,
            1 => 'ring-1',
            2 => 'ring-2',
            3 => 'ring',
            4 => 'ring-4',
            default => $ring,
        },
    ]);

    $hasLimitedRemainingText = $hasLimitedRemainingText();
    $isLimitedRemainingTextSeparate = $isLimitedRemainingTextSeparate();

    $limitedRemainingTextSizeClasses = match ($getLimitedRemainingTextSize()) {
        'xs' => 'text-xs',
        'sm', null => 'text-sm',
        'base', 'md' => 'text-base',
        'lg' => 'text-lg',
        default => $size,
    };
@endphp

<style>
    * {
        --swiper-theme-color: rgba(var(--primary-600));
        --swiper-scrollbar-drag-bg-color: rgba(var(--primary-600));
    }

    swiper-container {
        width: 100%;
        max-width: 100vw;
    }

    swiper-slide {
        text-align: center;
        font-size: 18px;
        background: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    swiper-slide img {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
</style>
<div {{ $attributes->merge($getExtraAttributes(), escape: false)->class(['fi-in-image flex items-center gap-x-2.5']) }}
     ax-load
     ax-load-src="{{ \Filament\Support\Facades\FilamentAsset::getScriptSrc('filament-swiper-scripts', 'rupadana/filament-swiper') }}"
     x-data="{
        isModalOpen: false,
        modalImageSrc: '',
        isPdf: false,
        showImageModal(imgSrc, isPdfFile = false) {
            this.modalImageSrc = imgSrc;
            this.isPdf = isPdfFile;
            $dispatch('open-modal', { id: 'image-modal' })
        }
    }">
    @if (count($limitedState))
        <x-filament::modal width="5xl" id="image-modal">
            <x-slot name="header">
                <div class="flex justify-between items-center" style="width: 97%;">
                    <h2 class="text-lg font-medium">{{__('Preview')}}</h2>
                    <template x-if="isPdf">
                        <a
                            :href="modalImageSrc"
                            download
                            class="bg-[#0f2c24] hover:bg-gray-400 text-white font-bold py-2 px-4 rounded inline-flex items-center"
                        >
                            <svg class="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="white">
                                <path d="M13 8V2H7v6H2l8 8 8-8h-5zM0 18h20v2H0v-2z"/>
                            </svg>
                            {{ __('Download PDF') }}
                        </a>
                    </template>
                </div>
            </x-slot>

            <div x-show="isPdf" class="w-full h-screen">
                <iframe :src="modalImageSrc + '#toolbar=0'" class="w-full h-full"></iframe>
            </div>
            <img x-show="!isPdf" :src="modalImageSrc" class="w-full h-auto" alt="Full size preview">
        </x-filament::modal>

        <swiper-container
            @if ($autoplay) autoplay-delay="{{ $autoplayDelay }}" autoplay="@js($autoplay)" @endif
        slides-per-view="@js($slidesPerView)" @if ($navigation) navigation="@js($navigation)" @endif
            @if ($pagination) pagination="@js($pagination)" pagination-type="{{ $paginationType }}" pagination-clickable="@js($paginationClickable)" pagination-dynamic-bullets="@js($paginationDynamicBullets)" pagination-dynamic-main-bullets="{{ $paginationDynamicMainBullets }}" pagination-hide-on-click="@js($paginationHideOnClick)" @endif
            @if ($scrollbar) scrollbar="@js($scrollbar)" scrollbar-draggable="@js($scrollbarDraggable)" scrollbar-drag-size="{{ $scrollbarDragSize }}" scrollbar-hide="@js($scrollbarHide)" scrollbar-snap-on-release="@js($scrollbarSnapOnRelease)" @endif
            effect="{{ $effect }}"
            @if ($effect == \Rupadana\FilamentSwiper\Infolists\Components\SwiperImageEntry::COVERFLOW_EFFECT) coverflow-effect-modifier="@js($getCoverflowEffectModifier())" coverflow-depth="@js($getCoverflowDepth())" coverflow-slide-shadows="@js($getCoverflowSlideShadows())" coverflow-stretch="@js($getCoverflowStretch())" @endif
            @if ($effect == \Rupadana\FilamentSwiper\Infolists\Components\SwiperImageEntry::CARDS_EFFECT) cards-per-slide-offset="@js($getCardsPerSlideOffset())" cards-per-slide-rotate="@js($getCardsPerSlideRotate())" cards-rotate="@js($getCardsRotate())" cards-slide-shadows="@js($getCardsSlideShadows())" @endif
            centered-slides="@js($getCenteredSlides())"
            style="z-index: 0">
            @foreach ($limitedState as $stateItem)
                <swiper-slide>
                    @if (is_array($stateItem) && isset($stateItem['mime_type']) && $stateItem['mime_type'] === 'application/pdf')
                        <div class="cursor-pointer w-full h-full flex items-center justify-center bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200"
                             @click="showImageModal('{{ $stateItem['url'] }}', true)">
                            <div class="flex flex-col items-center gap-2 relative">
                                <iframe
                                    src="{{ $stateItem['url'] }}#toolbar=0"
                                    class="w-full h-full rounded-lg"
                                    style="height: {{ $height }}; {{ $width ? "width: {$width};" : '' }}"
                                ></iframe>
                                <span class="text-sm text-gray-600 absolute top-2/4" style="background-color: #0f2c24; color: #fff; padding: 16px; font-weight: 600; opacity: .9" >{{ __('View PDF') }}</span>
                            </div>
                        </div>
                    @else
                        <img src="{{ filled($stateItem) ? (is_array($stateItem) ? $stateItem['url'] : $getImageUrl($stateItem)) : $defaultImageUrl }}"
                             {{ $getExtraImgAttributeBag()->class([
                                 'max-w-none object-cover object-center cursor-pointer',
                                 'rounded-full' => $isCircular,
                                 $ringClasses
                             ])->style([
                                 "height: {$height}" => $height,
                                 "width: {$width}" => $width,
                             ]) }}
                             @click="showImageModal($el.src, false)" />
                    @endif
                </swiper-slide>
            @endforeach
{{--            @foreach ($limitedState as $stateItem)--}}
{{--                <swiper-slide>--}}
{{--                    @if (is_array($stateItem) && isset($stateItem['mime_type']) && $stateItem['mime_type'] === 'application/pdf')--}}
{{--                        <div class="cursor-pointer w-full h-full flex items-center justify-center bg-gray-100 rounded-lg"--}}
{{--                             @click="showImageModal('{{ $stateItem['url'] }}', true)">--}}
{{--                            <iframe--}}
{{--                                src="{{ $stateItem['url'] }}#toolbar=0"--}}
{{--                                class="w-full h-full rounded-lg"--}}
{{--                                style="height: {{ $height }}; {{ $width ? "width: {$width};" : '' }}"--}}
{{--                            ></iframe>--}}
{{--                        </div>--}}
{{--                    @else--}}
{{--                        <img src="{{ filled($stateItem) ? (is_array($stateItem) ? $stateItem['url'] : $getImageUrl($stateItem)) : $defaultImageUrl }}"--}}
{{--                             {{ $getExtraImgAttributeBag()->class([--}}
{{--                                 'max-w-none object-cover object-center cursor-pointer',--}}
{{--                                 'rounded-full' => $isCircular,--}}
{{--                                 $ringClasses--}}
{{--                             ])->style([--}}
{{--                                 "height: {$height}" => $height,--}}
{{--                                 "width: {$width}" => $width,--}}
{{--                             ]) }}--}}
{{--                             @click="showImageModal($el.src, false)" />--}}
{{--                    @endif--}}
{{--                </swiper-slide>--}}
{{--            @endforeach--}}

            @if (
                $hasLimitedRemainingText &&
                    $loop->iteration < count($limitedState) &&
                    !$isLimitedRemainingTextSeparate &&
                    $isCircular)
                <div style="
                            @if ($height) height: {{ $height }}; @endif
                            @if ($width) width: {{ $width }}; @endif
                        "
                    @class([
                        'flex items-center justify-center bg-gray-100 font-medium text-gray-500 dark:bg-gray-800 dark:text-gray-400',
                        'rounded-full' => $isCircular,
                        $limitedRemainingTextSizeClasses,
                        $ringClasses,
                    ]) @style([
                        "height: {$height}" => $height,
                        "width: {$width}" => $width,
                    ])>
                    <span class="-ms-0.5">
                        +{{ count($state) - count($limitedState) }}
                    </span>
                </div>
            @endif
        </swiper-container>

        @if (
            $hasLimitedRemainingText &&
                $loop->iteration < count($limitedState) &&
                ($isLimitedRemainingTextSeparate || !$isCircular))
            <div @class([
                'font-medium text-gray-500 dark:text-gray-400',
                $limitedRemainingTextSizeClasses,
            ])>
                +{{ count($state) - count($limitedState) }}
            </div>
        @endif
    @elseif (($placeholder = $getPlaceholder()) !== null)
        <x-filament-infolists::entries.placeholder>
            {{ $placeholder }}
        </x-filament-infolists::entries.placeholder>
    @endif
</div>
