@php
    $id = $getId();
    $isContained = $getContainer()->getParentComponent()->isContained();

    $activeTabClasses = \Illuminate\Support\Arr::toCssClasses([
        'fi-active',
        'p-6 lg:ltr:pl-0 lg:rtl:pr-0' => $isContained,
        'mt-6' => ! $isContained,
    ]);

    $inactiveTabClasses = 'invisible absolute h-0 overflow-hidden p-0';
@endphp

<div
    x-bind:class="tab === @js($id) ? @js($activeTabClasses) : @js($inactiveTabClasses)"
    {{
        $attributes
            ->merge([
                'aria-labelledby' => $id,
                'id' => $id,
                'role' => 'tabpanel',
                'tabindex' => '0',
            ], escape: false)
            ->merge($getExtraAttributes(), escape: false)
            ->class(['fi-in-tabs-tab outline-none w-full lg:col-span-2'])
    }}
>
    {{ $getChildComponentContainer() }}
</div>
