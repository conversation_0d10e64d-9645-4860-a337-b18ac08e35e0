<x-filament-panels::page>
    {{-- Custom Styles for Responsive Dashboard --}}
    <style>
        /* Ensure chart widgets have consistent heights on larger screens */
        @media (min-width: 1024px) {
            .dashboard-chart-container {
                display: grid;
                align-items: stretch;
            }

            .dashboard-chart-container .dashboard-widget-container {
                display: flex;
                flex-direction: column;
            }

            .dashboard-chart-container .dashboard-widget-container > div {
                flex: 1;
                display: flex;
                flex-direction: column;
            }

            .dashboard-chart-container .fi-wi-chart {
                flex: 1;
                display: flex;
                flex-direction: column;
            }

            .dashboard-chart-container .fi-section {
                flex: 1;
                display: flex;
                flex-direction: column;
            }

            .dashboard-chart-container .fi-section-content {
                flex: 1;
                display: flex;
                flex-direction: column;
            }
        }

        /* Ensure widgets don't get cropped */
        .dashboard-widget-container {
            min-height: fit-content;
            overflow: visible;
        }

        /* Mobile responsiveness */
        @media (max-width: 767px) {
            .custom_widget_page .grid {
                gap: 1rem;
            }
        }

        /* Carousel Styles */
        .widget-carousel-container {
            position: relative;
            padding: 0 30px; /* Space for navigation arrows */
        }

        .widget-carousel-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            z-index: 10;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .widget-carousel-nav:hover {
            background: #f9fafb;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .widget-carousel-nav.prev {
            left: -20px;
        }

        .widget-carousel-nav.next {
            right: -20px;
        }

        .dark .widget-carousel-nav {
            background: #374151;
            border-color: #4b5563;
            color: #d1d5db;
        }

        .dark .widget-carousel-nav:hover {
            background: #4b5563;
        }

        /* Ensure carousel items maintain proper spacing */
        .widget-carousel-track {
            display: flex;
            transition: transform 0.3s ease-in-out;
        }

        .widget-carousel-item {
            flex: none;
            padding: 0 8px; /* Half of gap-4 */
        }
    </style>

    {{-- Welcome Widget --}}
    <div class="mb-0">
        @livewire(\App\Filament\Resources\WidgetResource\Widgets\WelcomeWidget::class)
    </div>

    <div class="grid grid-cols-12 gap-4 lg:gap-8 custom_widget_page">

        {{-- Widgets Section --}}
        <div class="col-span-12 lg:col-span-12 grid ">
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-4">

                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\TotalLeasesCount::class)

                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\TotalInvoicesCount::class)

                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\TotalPropertiesCount::class)

                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\TotalTicketsCount::class)

            </div>
        </div>

        {{-- Custom Widgets Section --}}


        @php
            // Count visible widgets based on permissions
            $visibleWidgets = 0;
            $widgets = [
                'widget_CompanyCount' => \App\Filament\Resources\WidgetResource\Widgets\CompanyCount::class,
                'widget_ActiveLeaseCount' => \App\Filament\Resources\WidgetResource\Widgets\ActiveLeaseCount::class,
                'widget_InactiveLeaseCount' => \App\Filament\Resources\WidgetResource\Widgets\InactiveLeaseCount::class,
                'widget_EjarRegisteredLeaseCount' => \App\Filament\Resources\WidgetResource\Widgets\EjarRegisteredLeaseCount::class,
                'widget_ActiveMaintenanceRequestsCount' => \App\Filament\Resources\WidgetResource\Widgets\ActiveMaintenanceRequestsCount::class,
                'widget_InactiveMaintenanceRequestsCount' => \App\Filament\Resources\WidgetResource\Widgets\InactiveMaintenanceRequestsCount::class,
                'widget_CommissionInvoicesTotal' => \App\Filament\Resources\WidgetResource\Widgets\CommissionInvoicesTotal::class,
                'widget_TotalPaidPayments' => \App\Filament\Resources\WidgetResource\Widgets\TotalPaidPayments::class,
                'widget_TotalUnpaidPayments' => \App\Filament\Resources\WidgetResource\Widgets\TotalUnpaidPayments::class,
                'widget_InactiveSubscription' => \App\Filament\Resources\WidgetResource\Widgets\InactiveSubscription::class,
                'widget_ActiveSubscription' => \App\Filament\Resources\WidgetResource\Widgets\ActiveSubscription::class,
                'widget_TotalSubscriptionIncome' => \App\Filament\Resources\WidgetResource\Widgets\TotalSubscriptionIncome::class,
                'widget_TotalSubscriptionUpcome' => \App\Filament\Resources\WidgetResource\Widgets\TotalSubscriptionUpcome::class,
                'widget_ProperitiesManagedCount' => \App\Filament\Resources\WidgetResource\Widgets\ProperitiesManagedCount::class,
                'widget_TotalRentCurrentMonth' => \App\Filament\Resources\WidgetResource\Widgets\TotalRentCurrentMonth::class,
            ];

            foreach ($widgets as $permission => $class) {
                if (auth()->user()->can($permission)) {
                    $visibleWidgets++;
                }
            }
        @endphp

        <div class="col-span-12 lg:col-span-12 {{ $visibleWidgets > 5 ? 'flex' : 'grid' }}">

            @if($visibleWidgets > 5)
                {{-- Carousel for more than 5 widgets --}}
                <div class="relative w-full" x-data="widgetCarousel()">
                    {{-- Navigation Arrows - Always visible --}}
                    <button
                        @click="prev()"
                        x-show="canGoPrev || canGoNext"
                        class="absolute left-0 top-1/2 -translate-y-1/2 z-20 bg-white dark:bg-gray-800 shadow-lg rounded-full p-1.5 sm:p-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-200 dark:border-gray-600"
                        style="margin-left: -8px;"
                        :class="{ 'opacity-50 cursor-not-allowed': isRTL ? !canGoNext : !canGoPrev }"
                    >
                        <svg class="w-4 h-4 sm:w-5 sm:h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>

                    <button
                        @click="next()"
                        x-show="canGoPrev || canGoNext"
                        class="absolute right-0 top-1/2 -translate-y-1/2 z-20 bg-white dark:bg-gray-800 shadow-lg rounded-full p-1.5 sm:p-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-200 dark:border-gray-600"
                        style="margin-right: -8px;"
                        :class="{ 'opacity-50 cursor-not-allowed': isRTL ? !canGoPrev : !canGoNext }"
                    >
                        <svg class="w-4 h-4 sm:w-5 sm:h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>

                    {{-- Carousel Container with Touch Support --}}
                    <div
                        class="overflow-hidden cursor-grab active:cursor-grabbing"
                        x-ref="carouselContainer"
                        @mousedown="startDrag($event)"
                        @touchstart="startDrag($event)"
                        @mousemove="drag($event)"
                        @touchmove="drag($event)"
                        @mouseup="endDrag()"
                        @touchend="endDrag()"
                        @mouseleave="endDrag()"
                    >
                        <div
                            class="flex transition-transform duration-300 ease-in-out gap-4"
                            :style="`transform: translateX(${getCurrentTransform()}%)`"
                            x-ref="carousel"
                        >
                            @foreach($widgets as $permission => $class)
                                @can($permission)
                                    <div class="flex-none w-full sm:w-1/2 md:w-1/3 lg:w-1/3 xl:w-1/4 2xl:w-1/5">
                                        @livewire($class)
                                    </div>
                                @endcan
                            @endforeach
                        </div>
                    </div>

                    {{-- Dots Indicator --}}
                    <div class="flex justify-center mt-4 space-x-2">
                        <template x-for="(dot, index) in totalSlides" :key="index">
                            <button
                                @click="goToSlide(index)"
                                class="w-2 h-2 rounded-full transition-colors"
                                :class="currentSlide === index ? 'bg-primary-500' : 'bg-gray-300 dark:bg-gray-600'"
                            ></button>
                        </template>
                    </div>
                </div>
            @else
                {{-- Regular Grid for 5 or fewer widgets --}}
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
                    @foreach($widgets as $permission => $class)
                        @can($permission)
                            @livewire($class)
                        @endcan
                    @endforeach
                </div>
            @endif
        </div>

        {{-- Charts Section --}}
        <div class="col-span-12 lg:col-span-12">
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 lg:gap-6 dashboard-chart-container">
                {{-- Lease Analytics Chart --}}
                <div class="col-span-1 dashboard-widget-container">
                    <div class="h-full min-h-[300px] lg:min-h-[350px]">
                        @livewire(\App\Filament\Resources\WidgetResource\Widgets\LeaseAnalyticsWidget::class)
                    </div>
                </div>

                {{-- Invoice Analytics Chart --}}
                <div class="col-span-1 dashboard-widget-container">
                    <div class="h-full min-h-[300px] lg:min-h-[350px]">
                        @livewire(\App\Filament\Resources\WidgetResource\Widgets\InvoiceAnalyticsWidget::class)
                    </div>
                </div>

                {{-- Property Analytics Chart --}}
                <div class="col-span-1 md:col-span-2 xl:col-span-1 dashboard-widget-container">
                    <div class="h-full min-h-[300px] lg:min-h-[350px]">
                        @livewire(\App\Filament\Resources\WidgetResource\Widgets\PropertyAnalyticsWidget::class)
                    </div>
                </div>
            </div>
        </div>

        {{-- Table Widgets Section --}}
        <div class="col-span-12">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
                {{-- Latest Leases Widget --}}
                <div class="col-span-1 dashboard-widget-container">
                    <div class="h-full">
                        @livewire(\App\Filament\Resources\WidgetResource\Widgets\LatestLeasesWidget::class)
                    </div>
                </div>

                {{-- Latest Invoices Widget --}}
                <div class="col-span-1 dashboard-widget-container">
                    <div class="h-full">
                        @livewire(\App\Filament\Resources\WidgetResource\Widgets\LatestInvoicesWidget::class)
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Carousel JavaScript --}}
    <script>
        function widgetCarousel() {
            return {
                currentSlide: 0,
                totalSlides: 0,
                slideWidth: 0,
                itemsPerSlide: 5, // Default for 2xl screens
                isDragging: false,
                startX: 0,
                currentX: 0,
                dragThreshold: 50, // Minimum distance to trigger slide change
                isRTL: false,

                init() {
                    // Detect RTL direction
                    this.isRTL = document.documentElement.dir === 'rtl' ||
                                document.body.dir === 'rtl' ||
                                getComputedStyle(document.documentElement).direction === 'rtl';

                    this.calculateSlides();
                    this.updateItemsPerSlide();

                    // Recalculate on window resize
                    window.addEventListener('resize', () => {
                        this.updateItemsPerSlide();
                        this.calculateSlides();
                        // Ensure current slide is still valid
                        if (this.currentSlide >= this.totalSlides) {
                            this.currentSlide = Math.max(0, this.totalSlides - 1);
                        }
                    });
                },

                updateItemsPerSlide() {
                    const width = window.innerWidth;
                    if (width >= 1536) { // 2xl
                        this.itemsPerSlide = 5;
                    } else if (width >= 1280) { // xl
                        this.itemsPerSlide = 4;
                    } else if (width >= 1024) { // lg
                        this.itemsPerSlide = 3;
                    } else if (width >= 768) { // md
                        this.itemsPerSlide = 3;
                    } else if (width >= 640) { // sm
                        this.itemsPerSlide = 2;
                    } else {
                        this.itemsPerSlide = 1;
                    }
                },

                calculateSlides() {
                    const totalItems = {{ $visibleWidgets }};
                    this.totalSlides = Math.max(1, Math.ceil(totalItems / this.itemsPerSlide));
                    this.slideWidth = 100 / this.itemsPerSlide;
                },

                next() {
                    if (this.isRTL) {
                        // In RTL, "next" means going to previous slide
                        if (this.canGoPrev) {
                            this.currentSlide--;
                        }
                    } else {
                        if (this.canGoNext) {
                            this.currentSlide++;
                        }
                    }
                },

                prev() {
                    if (this.isRTL) {
                        // In RTL, "prev" means going to next slide
                        if (this.canGoNext) {
                            this.currentSlide++;
                        }
                    } else {
                        if (this.canGoPrev) {
                            this.currentSlide--;
                        }
                    }
                },

                goToSlide(index) {
                    this.currentSlide = index;
                },

                // Touch/Mouse drag functionality
                startDrag(event) {
                    this.isDragging = true;
                    this.startX = this.getEventX(event);
                    this.currentX = this.startX;

                    // Disable transitions during drag
                    this.$refs.carousel.style.transition = 'none';

                    // Prevent default to avoid text selection
                    event.preventDefault();
                },

                drag(event) {
                    if (!this.isDragging) return;

                    this.currentX = this.getEventX(event);
                    let deltaX = this.currentX - this.startX;

                    // Reverse delta for RTL
                    if (this.isRTL) {
                        deltaX = -deltaX;
                    }

                    // Apply drag effect with resistance
                    const dragPercent = (deltaX / this.$refs.carouselContainer.offsetWidth) * 100;
                    const currentTransform = this.getCurrentTransform();
                    const newTransform = currentTransform + dragPercent;

                    this.$refs.carousel.style.transform = `translateX(${newTransform}%)`;
                },

                endDrag() {
                    if (!this.isDragging) return;

                    this.isDragging = false;

                    // Re-enable transitions
                    this.$refs.carousel.style.transition = 'transform 0.3s ease-in-out';

                    let deltaX = this.currentX - this.startX;

                    // Reverse delta for RTL
                    if (this.isRTL) {
                        deltaX = -deltaX;
                    }

                    // Determine if we should change slides
                    if (Math.abs(deltaX) > this.dragThreshold) {
                        if (deltaX > 0 && this.canGoPrev) {
                            this.prev();
                        } else if (deltaX < 0 && this.canGoNext) {
                            this.next();
                        } else {
                            // Snap back to current slide
                            this.$refs.carousel.style.transform = `translateX(${this.getCurrentTransform()}%)`;
                        }
                    } else {
                        // Snap back to current slide
                        this.$refs.carousel.style.transform = `translateX(${this.getCurrentTransform()}%)`;
                    }
                },

                getEventX(event) {
                    return event.type.includes('mouse') ? event.clientX : event.touches[0].clientX;
                },

                getCurrentTransform() {
                    if (this.isRTL) {
                        // In RTL, we need to calculate from the right
                        return (this.currentSlide * this.slideWidth);
                    } else {
                        return -(this.currentSlide * this.slideWidth);
                    }
                },

                get canGoNext() {
                    return this.currentSlide < this.totalSlides - 1;
                },

                get canGoPrev() {
                    return this.currentSlide > 0;
                }
            }
        }
    </script>
</x-filament-panels::page>
