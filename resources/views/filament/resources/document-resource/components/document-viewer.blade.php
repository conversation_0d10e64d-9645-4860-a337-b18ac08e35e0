@php
    $collection = $documentType && $documentType->key == \App\Enums\DocumentTypeEnum::Ownership->value
        ? 'ownership_document'
        : 'upload_contract';
    $mediaItems = $getRecord()->getMedia($collection) ?? collect();
    $totalSlides = $mediaItems->count();
@endphp

<div class="space-y-4"
     x-data="{
         currentSlide: 0,
         totalSlides: {{ $totalSlides }},
         nextSlide() {
             this.currentSlide = (this.currentSlide + 1) % this.totalSlides;
         },
         previousSlide() {
             this.currentSlide = (this.currentSlide - 1 + this.totalSlides) % this.totalSlides;
         }
     }">

    @if($totalSlides > 0)
        <div class="relative">
            <div class="overflow-hidden">
                <div class="flex transition-transform duration-300 ease-in-out"
                     x-bind:style="{ transform: `translateX(-${currentSlide * 100}%)` }">
                    @foreach($mediaItems as $index => $media)
                        <div class="w-full flex-shrink-0">
                            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                                <div class="p-2 flex justify-end space-x-2 border-b">
                                    <a href="{{ $media->getUrl() }}"
                                       target="_blank"
                                       class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700">
                                        {{ __('View PDF') }}
                                    </a>
                                    <a href="{{ $media->getUrl() }}"
                                       download
                                       class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">
                                        {{ __('Download') }}
                                    </a>
                                </div>
                                <div class="relative bg-gray-100" style="height: 800px;">
                                    @if(str_contains($media->mime_type, 'pdf'))
                                        <iframe
                                            src="{{ $media->getUrl() }}#view=FitH"
                                            class="w-full h-full"
                                            frameborder="0"
                                        ></iframe>
                                    @else
                                        <img
                                            src="{{ $media->getUrl() }}"
                                            alt="{{ $media->file_name }}"
                                            class="w-full h-full object-contain"
                                        />
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>


            @if($totalSlides > 1)
                <div class="absolute inset-y-0 left-0 flex items-center">
                    <button
                        @click="previousSlide"
                        class="p-2 bg-primary-600 hover:bg-primary-700 rounded-r-lg">
                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                        </svg>
                    </button>
                </div>
                <div class="absolute inset-y-0 right-0 flex items-center">
                    <button
                        @click="nextSlide"
                        class="p-2 bg-primary-600 hover:bg-primary-700 rounded-l-lg">
                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </button>
                </div>
            @endif
        </div>
    @else
        <div class="text-center text-gray-500 py-4 bg-gray-50 rounded-lg">
            {{ __('No documents available') }}
        </div>
    @endif
</div>

<style>
    iframe {
        width: 100%;
        height: 100%;
        border: none;
    }
</style>
