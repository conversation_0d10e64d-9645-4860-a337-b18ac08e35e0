<x-filament-panels::page>
    <div class="mx-auto max-w-7xl">
        @if($currentSubscription)
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700">
                <!-- Header -->
                <div class="p-6 border-b border-gray-100 dark:border-gray-700">
                    <div class="flex justify-between items-start">
                        <div>
                            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">{{__('Current Subscription Plan')}}</h2>
                            <p class="text-2xl uppercase text-md font-bold  text-primary-600 dark:text-primary-400">{{ $currentSubscription->plan->name }}</p>
                            <p class="mt-2 text-gray-500 dark:text-gray-300">{{ $currentSubscription->plan->description }}</p>
                        </div>
                        <div class="text-right">
               <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium
                        {{ match($currentSubscription->status) {
                            'new' => 'bg-blue-100 text-blue-800 dark:bg-blue-800/20 dark:text-blue-400',
                            'canceled' => 'bg-red-100 text-red-800 dark:bg-red-800/20 dark:text-red-400',
                            'paid' => 'bg-green-100 text-green-800 dark:bg-green-800/20 dark:text-green-400',
                            'finished' => 'bg-gray-100 text-gray-800 dark:bg-gray-800/20 dark:text-gray-400',
                            'active' => 'bg-emerald-100 text-emerald-800 dark:bg-emerald-800/20 dark:text-emerald-400',
                            default => 'bg-gray-100 text-gray-800 dark:bg-gray-800/20 dark:text-gray-400'
                        } }}">
                        {{ ucfirst(Modules\Subscription\app\Enums\SubscriptionStatusEnum::trans($currentSubscription->status)) }}
                    </span>
                        </div>
                    </div>
                </div>

                <!-- Content -->
                <div class="p-6">
                    <!-- Price -->
                    <div class="mb-6">
                        <span class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($currentSubscription->price, 2)}} <span class="icon-saudi_riyal"></span></span>
                        <span class="text-gray-500 dark:text-gray-400">/{{__('month')}}</span>
                    </div>

                    <!-- Dates -->
                    <div class="space-y-3 mb-6">
                        <div class="flex justify-start gap-3">
                            <span class="text-sm text-gray-500 dark:text-gray-400">{{__('Start Date:')}}</span>
                            <span class="text-sm text-gray-900 dark:text-white">{{ \Carbon\Carbon::parse($currentSubscription->start_at)->format('M d, Y') }}</span>
                        </div>

                        @if($currentSubscription->expired_at)
                            <div class="flex justify-start gap-3">
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{__('Expiry Date:')}}</span>
                                <span class="text-sm text-gray-900 dark:text-white">{{ \Carbon\Carbon::parse($currentSubscription->expired_at)->format('M d, Y') }}</span>
                            </div>
                        @endif

                        @if($currentSubscription->canceled_at)
                            <div class="flex justify-start gap-3">
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{__('Cancelled Date')}}</span>
                                <span class="text-sm text-gray-900 dark:text-white">{{ \Carbon\Carbon::parse($currentSubscription->canceled_at)->format('M d, Y') }}</span>
                            </div>
                        @endif
                    </div>

                    <!-- Features -->
                    @if($currentSubscription->plan->planFeatures->isNotEmpty())
                        <div>
                            <span class="text-gray-400 text-bold">{{__('Plan Features:')}}</span>
                            <ul class="space-y-2">
                                @foreach($currentSubscription->plan->planFeatures as $planFeature)
                                    <li class="flex items-center">
                                        <svg class="w-5 h-5 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span class="ml-3 text-white-200 dark:text-gray-300">
                                            {{ $planFeature->feature->name }}
                                            @if($planFeature->value)
                                                - {{ $planFeature->value }}
                                            @endif
                                        </span>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                </div>
            </div>
        @else
            <div class="min-h-[50px] flex items-center justify-center">
                <div class="text-center">
                    <div class="text-gray-400 mb-4">
                        <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">{{__('No Active Subscription')}}</h3>
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                        {{__(' Choose a plan above to start your subscription.')}}
                    </p>
                </div>
            </div>
        @endif
        <!-- Interval Tabs - Only show if plans exist for that interval -->
            @if($this->plans->count() > 0)
                <div class="mb-8 mt-8 border-gray-200 dark:border-gray-700">
                    <div class="text-center mb-8">
                        <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">{{__('Choose Your Perfect Plan')}}</h2>
                        <p class="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                            {{__('Select the best subscription that fits your needs. Switch between billing intervals to find the perfect option.')}}
                        </p>
                    </div>
                    <ul class="flex flex-wrap justify-center -mb-px text-sm font-medium text-center" role="tablist">
                        <ul class="flex flex-wrap justify-center -mb-px text-sm font-medium text-center" role="tablist">
                            @if($this->plans->where('interval.value', 'month')->count() > 0)
                                <li class="mr-2" role="presentation">
                                    <button
                                        wire:click="$set('selectedInterval', 'month')"
                                        class="text-2xl font-bold inline-block p-4 rounded-t-lg {{ $selectedInterval === 'month' ? 'text-primary-600 border-b-2 border-primary-600 active' : 'text-gray-500 hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300 ' }}"
                                    >
                                        {{__('Monthly')}}
                                    </button>
                                </li>
                            @endif

                            @if($this->plans->where('interval.value', 'year')->count() > 0)
                                <li class="mr-2" role="presentation">
                                    <button
                                        wire:click="$set('selectedInterval', 'year')"
                                        class="text-2xl font-bold inline-block p-4 rounded-t-lg {{ $selectedInterval === 'year' ? 'text-primary-600 border-b-2 border-primary-600 active' : 'text-gray-500 hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300' }}"
                                    >
                                        {{__(' Annually')}}
                                    </button>
                                </li>
                            @endif

                            @if($this->plans->where('interval.value', 'day')->count() > 0)
                                <li class="mr-2" role="presentation">
                                    <button
                                        wire:click="$set('selectedInterval', 'day')"
                                        class="inline-block p-4 rounded-t-lg {{ $selectedInterval === 'day' ? 'text-primary-600 border-b-2 border-primary-600 active' : 'text-gray-500 hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300' }}"
                                    >
                                        {{__('Daily')}}
                                    </button>
                                </li>
                            @endif
                        </ul>
                    </ul>
                </div>

                <!-- Plans Grid -->
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-2 mt-4">
                    @foreach($this->filteredPlans as $plan)
                        <div class="flex flex-col h-full overflow-hidden bg-white rounded-lg shadow-lg dark:bg-gray-800 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-xl">
                            <div class="flex-grow px-6 py-8 relative">
                                <!-- Popular Badge (if applicable) -->
                                @if($plan->is_default)
                                    <div class="absolute top-0 right-0 px-3 py-1 bg-primary-500 text-white text-sm font-semibold rounded-bl">
                                        {{__('Popular')}}
                                    </div>
                                @endif

                                <div class="flex justify-between items-center">
                                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white">
                                        {{ $plan->name }}
                                    </h3>
                                    @if($plan->discount_price)
                                        <div class="flex flex-col items-end">
                                    <span class="text-lg line-through text-gray-800 font-bold dark:text-gray-200">
                                        ${{ number_format($plan->price, 2)}} <span class="icon-saudi_riyal"></span>
                                    </span>
                                            <span class="text-2xl font-bold text-primary-600 dark:text-primary-400">
                                        {{ number_format($plan->discount_price, 2) }} <span class="icon-saudi_riyal"></span>
                                    </span>
                                        </div>
                                    @else
                                        <span class="text-2xl font-bold text-primary-600">
                                    ${{ number_format($plan->price, 2) }}
                                </span>
                                    @endif
                                </div>

                                <p class="mt-2 text-gray-500 dark:text-gray-300">
                                    {{ $plan->description }}
                                </p>

                                <div class="mt-4">
                            <span class="text-2xl uppercase text-md font-bold  text-primary-600 dark:text-primary-400">
                                {{ $plan->interval_count }} {{ Str::plural($plan->interval->value, $plan->interval_count) }}
                            </span>
                                </div>

                                <ul class="mt-6 space-y-4">
                                    <span class="text-gray-400 text-bold">{{__('Features:')}}</span>
                                    @foreach($plan->planFeatures as $planFeature)
                                            <li class="flex items-center">
                                                <svg class="w-5 h-5 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                                <span class="ml-3 text-white-200 dark:text-gray-300">
                                            {{ $planFeature->feature->name }}
                                                    @if($planFeature->value)
                                                        - {{ $planFeature->value }}
                                                    @endif
                                        </span>
                                            </li>
                                    @endforeach
                                </ul>
                            </div>

                            <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700 mt-auto">
                                <button
                                    wire:click="subscribe({{ $plan->id }})"
                                    @if($currentSubscription && $currentSubscription->plan_id === $plan->id)
                                        disabled
                                    class="w-full px-4 py-2 text-sm font-medium text-white bg-gray-400 rounded-md cursor-not-allowed"
                                    @else
                                        class="w-full px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                                    @endif
                                >
                                    @if($currentSubscription && $currentSubscription->plan_id === $plan->id)
                                        {{__('Current Plan')}}
                                    @else
                                        {{__('Subscribe')}}
                                    @endif
                                </button>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-12">
                    <div class="max-w-2xl mx-auto">
                        <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                            {{__('No Subscription Plans Available')}}
                        </h2>
                        <p class="text-gray-600 dark:text-gray-400">
                            {{__('Currently, there are no subscription plans available. Please check back later or contact support for more information.')}}
                        </p>
                    </div>
                </div>
            @endif

            <div x-data="{ shown: @entangle('showPaymentModal') }">
                <div
                    x-show="shown"
                    class="fixed inset-0 overflow-y-auto z-50"
                    style="display: none;"
                >
                    <div class="flex items-center justify-center min-h-screen px-4">
                        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>

                        <div class="relative bg-white rounded-lg overflow-hidden shadow-xl w-full max-w-4xl">
                            <!-- Modal header -->
                            <div class="flex items-center justify-between p-4 border-b">
                                <h3 class="text-lg font-semibold">{{__('Payment')}}</h3>
                                <button
                                    type="button"
                                    class="text-gray-400 hover:text-gray-500"
                                    wire:click="$set('showPaymentModal', false)"
                                >
                                    <span class="sr-only">{{__('close')}}</span>
                                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>

                            <!-- Modal body -->
                            <div class="p-4">
                                @if($paymentUrl)
                                    <div id="payment-container">
                                        <iframe
                                            id="payment-iframe"
                                            src="{{ $paymentUrl }}"
                                            frameborder="0"
                                            class="w-full"
                                            style="height: 600px;"
                                            onload="handleIframeLoad(this)"
                                        ></iframe>
                                    </div>
                                @endif

                                @if($paymentResponse)
                                    <div class="bg-white p-6 rounded-lg shadow-lg">
                                        <div class="flex items-center mb-4">
                                            @if($paymentResponse['success'])
                                                <svg class="w-6 h-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                            @endif
                                            <h2 class="text-xl font-semibold">{{ $paymentResponse['message'] }}</h2>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <script>
                function handleIframeLoad(iframe) {
                    try {
                        const iframeContent = iframe.contentDocument || iframe.contentWindow.document;
                        const bodyText = iframeContent.body.textContent.trim();

                        if (bodyText) {
                            try {
                                const response = JSON.parse(bodyText);
                                if (response.success && response.message === "Payment was successful!") {
                                    // Hide the iframe
                                    document.getElementById('payment-container').style.display = 'none';

                                    // Send response to Livewire component
                                @this.handlePaymentResponse(response);
                                }
                            } catch (e) {
                                console.log('Not JSON or different format');
                            }
                        }
                    } catch (e) {
                        console.log('Cross-origin restrictions');
                    }
                }
            </script>
    </div>
</x-filament-panels::page>
