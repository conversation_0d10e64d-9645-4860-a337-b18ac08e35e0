<x-filament-widgets::widget class="fi-wi-chart">
    <x-filament::section>
        {{-- Widget Header with Chart Type Buttons --}}
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-base font-semibold leading-6 text-gray-950 dark:text-white">
                {{ __('Lease Analytics ')}}
            </h3>

            {{-- Chart Type Buttons --}}
            <div class="flex gap-1" x-data="{ lastClick: 0, cooldown: false }">
                <button
                    wire:click="changeChartType('bar')"
                    @click="
                        if (cooldown) return;
                        cooldown = true;
                        setTimeout(() => cooldown = false, 1500);
                    "
                    :disabled="cooldown"
                    :class="cooldown ? 'opacity-50 cursor-not-allowed' : ''"
                    class="px-2 py-1 text-xs rounded transition-all {{ $chartType === 'bar' ? 'bg-primary-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}"
                    title="Bar Chart"
                >
                    📊
                </button>
                <button
                    wire:click="changeChartType('pie')"
                    @click="
                        if (cooldown) return;
                        cooldown = true;
                        setTimeout(() => cooldown = false, 1500);
                    "
                    :disabled="cooldown"
                    :class="cooldown ? 'opacity-50 cursor-not-allowed' : ''"
                    class="px-2 py-1 text-xs rounded transition-all {{ $chartType === 'pie' ? 'bg-primary-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}"
                    title="Pie Chart"
                >
                    🥧
                </button>
                <button
                    wire:click="changeChartType('line')"
                    @click="
                        if (cooldown) return;
                        cooldown = true;
                        setTimeout(() => cooldown = false, 1500);
                    "
                    :disabled="cooldown"
                    :class="cooldown ? 'opacity-50 cursor-not-allowed' : ''"
                    class="px-2 py-1 text-xs rounded transition-all {{ $chartType === 'line' ? 'bg-primary-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}"
                    title="Line Chart"
                >
                    📈
                </button>
                <button
                    wire:click="changeChartType('doughnut')"
                    @click="
                        if (cooldown) return;
                        cooldown = true;
                        setTimeout(() => cooldown = false, 1500);
                    "
                    :disabled="cooldown"
                    :class="cooldown ? 'opacity-50 cursor-not-allowed' : ''"
                    class="px-2 py-1 text-xs rounded transition-all {{ $chartType === 'doughnut' ? 'bg-primary-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}"
                    title="Doughnut Chart"
                >
                    🍩
                </button>
            </div>
        </div>

        {{-- Filter Dropdown --}}
        <div class="mb-4" x-data="{ filterCooldown: false }">
            <select
                wire:model.live="filter"
                @change="
                    filterCooldown = true;
                    setTimeout(() => filterCooldown = false, 1500);
                "
                :disabled="filterCooldown"
                :class="filterCooldown ? 'opacity-50 cursor-not-allowed' : ''"
                class="block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm transition-all"
            >
                <option value="status">{{ __('By Status ')}}</option>
                <option value="lease_type">{{ __('By Lease Type')}}</option>
            </select>
        </div>

        {{-- Chart Container with Fixed Height --}}
        <div class="relative" style="height: 350px;">
            <canvas
                x-data="{
                    chart: null,
                    isUpdating: false,
                    updateTimeout: null,

                    init() {
                        this.initChart()

                        // Listen for Livewire updates
                        Livewire.on('refreshLeaseChart', () => {
                            this.debouncedUpdate()
                        })
                    },

                    debouncedUpdate() {
                        if (this.updateTimeout) {
                            clearTimeout(this.updateTimeout)
                        }

                        this.updateTimeout = setTimeout(() => {
                            this.updateChart()
                        }, 150)
                    },

                    initChart() {
                        if (typeof Chart === 'undefined') {
                            setTimeout(() => this.initChart(), 100)
                            return
                        }

                        this.createChart()
                    },

                    createChart() {
                        if (this.isUpdating) return
                        this.isUpdating = true

                        try {
                            // Safely destroy existing chart
                            this.safeDestroy()

                            // Wait a bit for cleanup
                            setTimeout(() => {
                                this.buildChart()
                            }, 50)

                        } catch (error) {
                            console.error('Chart creation error:', error)
                            this.isUpdating = false
                        }
                    },

                    safeDestroy() {
                        if (this.chart) {
                            try {
                                // Stop animations first
                                if (typeof this.chart.stop === 'function') {
                                    this.chart.stop()
                                }

                                // Clear any pending animations
                                if (this.chart.ctx && this.chart.ctx.canvas) {
                                    const canvas = this.chart.ctx.canvas
                                    const ctx = canvas.getContext('2d')
                                    if (ctx) {
                                        ctx.clearRect(0, 0, canvas.width, canvas.height)
                                    }
                                }

                                // Destroy the chart
                                this.chart.destroy()

                            } catch (e) {
                                console.warn('Chart destruction warning:', e)
                            } finally {
                                this.chart = null
                            }
                        }
                    },

                    buildChart() {
                        try {
                            const canvas = this.$el
                            if (!canvas || !canvas.getContext) {
                                throw new Error('Invalid canvas element')
                            }

                            // Get fresh context
                            const ctx = canvas.getContext('2d')
                            if (!ctx) {
                                throw new Error('Cannot get canvas context')
                            }

                            // Clear canvas completely
                            ctx.clearRect(0, 0, canvas.width, canvas.height)

                            // Reset canvas size to ensure proper rendering
                            canvas.style.width = '100%'
                            canvas.style.height = '100%'

                            // Create new chart
                            this.chart = new Chart(ctx, {
                                type: '{{ $chartType }}',
                                data: @js($this->getCachedData()),
                                options: @js($this->getOptions())
                            })

                        } catch (error) {
                            console.error('Chart build error:', error)
                        } finally {
                            this.isUpdating = false
                        }
                    },

                    updateChart() {
                        this.createChart()
                    },

                    destroy() {
                        if (this.updateTimeout) {
                            clearTimeout(this.updateTimeout)
                        }
                        this.safeDestroy()
                    }
                }"
                wire:key="{{ $chartType }}-{{ $filter }}"
            ></canvas>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>

<script>
    // Load Chart.js if not already loaded
    if (typeof Chart === 'undefined') {
        const script = document.createElement('script')
        script.src = 'https://cdn.jsdelivr.net/npm/chart.js'
        script.async = true
        document.head.appendChild(script)
    }

    // Also handle Livewire navigation
    document.addEventListener('livewire:navigated', () => {
        if (typeof Chart === 'undefined') {
            const script = document.createElement('script')
            script.src = 'https://cdn.jsdelivr.net/npm/chart.js'
            script.async = true
            document.head.appendChild(script)
        }
    })
</script>
