@php
    $user = filament()->auth()->user();
@endphp

<x-filament-widgets::widget class="fi-welcome-widget">
    <x-filament::section>
        <div class="flex items-center gap-x-6">
            {{-- Avatar with user initials --}}
            <div class="flex-shrink-0">
                <x-filament-panels::avatar.user
                    size="xl"
                    :user="$user"
                    class="w-20 h-20 ring-4 ring-white dark:ring-gray-800 shadow-lg"
                />
            </div>

            {{-- Welcome message and user name --}}
            <div class="flex-1">
                <h1 class="text-2xl font-bold text-gray-950 dark:text-white mb-1">
                    {{ __('Welcome') }}
                </h1>
                <p class="text-xl text-gray-700 dark:text-gray-200 font-medium">
                    {{ filament()->getUserName($user) }}
                </p>
                @if($user->email)
                    <p class="text-sm text-gray-600 dark:text-gray-300 mt-2">
                        {{ $user->email }}
                    </p>
                @endif
            </div>

            {{-- Current date/time --}}
            <div class="hidden md:block text-right">
                <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {{ now()->format('l') }}
                    </p>
                    <p class="text-lg font-bold text-primary-600 dark:text-primary-400">
                        {{ now()->format('M j') }}
                    </p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        {{ now()->format('g:i A') }}
                    </p>
                </div>
            </div>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
