
<x-filament::dropdown
    placement="bottom-start"
    size
    teleport
    :attributes="
        \Filament\Support\prepare_inherited_attributes($attributes)
            ->class(['fi-tenant-menu'])
    "
>

    <x-slot name="trigger">
        <button
        @if (filament()->isSidebarCollapsibleOnDesktop())
            x-data="{ tooltip: false }"
            x-effect="
                tooltip = $store.sidebar.isOpen
                    ? false
                    : {
                        content: @js(auth()->user()->company->name),
                        placement: document.dir === 'rtl' ? 'left' : 'right',
                        theme: $store.theme,
                    }
            "
            x-tooltip.html="tooltip"
        @endif
        type="button"
        class="fi-tenant-menu-trigger group flex w-full items-center gap-x-3 rounded-lg p-2 text-sm font-medium outline-none transition duration-75 hover:bg-gray-100 focus-visible:bg-gray-100 dark:hover:bg-white/5 dark:focus-visible:bg-white/5"
        x-bind:class="
            $store.sidebar.isOpen
                ? 'justify-start'
                : 'justify-center'
        "
        >
            <x-filament-panels::avatar.tenant
                :tenant="auth()->user()->company"
                class="shrink-0"
            />

            <span
                @if (filament()->isSidebarCollapsibleOnDesktop())
                    x-show="$store.sidebar.isOpen"
                @endif
                class="grid justify-items-start text-start"
            >
                <span class="text-white group-hover:text-gray-900 dark:group-hover:text-white">
                    {{ auth()->user()->company->name }}
                </span>
            </span>

            @if(auth()->user()->companies->count() > 1)
            <x-filament::icon
                icon="heroicon-m-chevron-down"
                icon-alias="panels::tenant-menu.toggle-button"
                :x-show="filament()->isSidebarCollapsibleOnDesktop() ? '$store.sidebar.isOpen' : null"
                class="ms-auto h-5 w-5 shrink-0 text-gray-400 transition duration-75 group-hover:text-gray-500 group-focus-visible:text-gray-500 dark:text-gray-500 dark:group-hover:text-gray-400 dark:group-focus-visible:text-gray-400"
            />
            @endif
        </button>
    </x-slot>

    @if (auth()->user()->companies->count() > 1)
        <x-filament::dropdown.list>
            @foreach (auth()->user()->companies as $company)
              @if ($company->id !== auth()->user()->company_id)
                <form
                  x-data="{
                      switchCompany(event) {
                          event.preventDefault()

                          fetch(event.target.action, {
                              method: 'POST',
                              headers: {
                                  'Content-Type': 'application/json',
                                  'X-CSRF-TOKEN': document.querySelector('input[name=_token]').value
                              },
                              body: JSON.stringify({
                                  company_id: event.target.querySelector('input[name=company_id]').value
                              })
                          })

                          window.Livewire.navigate('/')
                      }
                  }"
                  @submit="switchCompany"
                  action="{{ route('switch-company') }}"
                  method="POST"
                >
                    @csrf
                    <input type="hidden" name="company_id" value="{{ $company->id }}">
                    <x-filament::dropdown.list.item
                        :image="filament()->getTenantAvatarUrl($company)"
                        tag="button"
                        type="submit"
                        class="w-full cursor-pointer"
                    >
                        {{ $company->name }}
                    </x-filament::dropdown.list.item>
                </form>
              @endif
            @endforeach
        </x-filament::dropdown.list>
    @endif
</x-filament::dropdown>
